#!/usr/bin/env python3
"""
Debug CSV loading issue
"""

import pandas as pd

def debug_csv():
    """Debug the CSV loading"""
    try:
        print("🔍 Debugging CSV loading...")
        
        # Read CSV file
        df = pd.read_csv('sample_data.csv')
        print(f"📁 Raw CSV loaded: {len(df)} rows")
        print(f"📊 Columns: {list(df.columns)}")
        print(f"📊 Data types: {df.dtypes}")
        print(f"📊 First few rows:")
        print(df.head(3))
        
        # Test numeric conversion
        print(f"\n🔢 Testing numeric conversion:")
        for col in ['Open', 'High', 'Low', 'Close', 'Volume']:
            if col in df.columns:
                print(f"   {col}: {df[col].dtype} -> ", end="")
                numeric_data = pd.to_numeric(df[col], errors='coerce')
                print(f"{numeric_data.dtype}, nulls: {numeric_data.isnull().sum()}")
                if numeric_data.isnull().sum() > 0:
                    print(f"      Sample values: {df[col].head().tolist()}")
        
        # Test date conversion
        print(f"\n📅 Testing date conversion:")
        date_col = 'Date'
        if date_col in df.columns:
            print(f"   {date_col}: {df[date_col].dtype} -> ", end="")
            try:
                date_data = pd.to_datetime(df[date_col])
                print(f"datetime64, nulls: {date_data.isnull().sum()}")
            except Exception as e:
                print(f"ERROR: {e}")
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    debug_csv()
