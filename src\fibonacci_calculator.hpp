#pragma once
#include <vector>
#include <array>
#include <cmath>
#include <algorithm>
#include <memory>

namespace trading {

struct OHLCBar {
    double open;
    double high;
    double low;
    double close;
    double volume;
    long long timestamp;
    
    OHLCBar(double o, double h, double l, double c, double v, long long t)
        : open(o), high(h), low(l), close(c), volume(v), timestamp(t) {}
};

struct FibonacciLevel {
    double level;      // Fibonacci ratio (e.g., 0.0, 1.0, 1.1, etc.)
    double price;      // Actual price at this level
    
    FibonacciLevel(double l, double p) : level(l), price(p) {}
};

struct FibonacciResult {
    std::vector<FibonacciLevel> levels;
    double range_high;
    double range_low;
    double price_range;
    long long start_time;
    long long end_time;
    bool is_bullish;
    
    // Key levels for signals
    double green_level;  // 1.0 level
    double red_level;    // 0.0 level
    double level_0_9;    // 0.9 level for long stop loss
    double level_0_1;    // 0.1 level for short stop loss
};

struct TradingSignal {
    enum Type { BUY, SELL, TAKE_PROFIT, STOP_LOSS };
    
    Type type;
    double price;
    long long timestamp;
    int bar_index;
    bool is_delayed;
    
    TradingSignal(Type t, double p, long long ts, int idx, bool delayed = false)
        : type(t), price(p), timestamp(ts), bar_index(idx), is_delayed(delayed) {}
};

class FibonacciCalculator {
private:
    // Fibonacci levels matching your Pine Script
    static constexpr std::array<double, 8> DEFAULT_FIB_LEVELS = {1.1, 1.08, 1.0, 0.9, 0.1, 0.0, -0.08, -0.1};
    
    std::vector<OHLCBar> data_;
    std::vector<FibonacciResult> fibonacci_results_;
    std::vector<TradingSignal> signals_;
    
    // Configuration
    bool use_mtf_mode_ = false;
    int mtf_period_ = 5;  // MTF timeframe in minutes
    int current_tf_period_ = 1;  // Current timeframe in minutes
    bool use_confirmed_data_ = true;
    int max_bars_back_ = 50;
    
    // Signal tracking
    bool green_signal_triggered_ = false;
    bool red_signal_triggered_ = false;
    bool active_buy_position_ = false;
    bool active_sell_position_ = false;
    double buy_tp_level_ = std::numeric_limits<double>::quiet_NaN();
    double sell_tp_level_ = std::numeric_limits<double>::quiet_NaN();
    double buy_sl_level_ = std::numeric_limits<double>::quiet_NaN();
    double sell_sl_level_ = std::numeric_limits<double>::quiet_NaN();

public:
    FibonacciCalculator() = default;
    
    // Configuration methods
    void setMTFMode(bool enabled, int mtf_period = 5, int current_tf_period = 1);
    void setConfirmedData(bool use_confirmed);
    void setMaxBarsBack(int max_bars);
    
    // Data management
    void setData(const std::vector<OHLCBar>& data);
    void addBar(const OHLCBar& bar);
    
    // Core calculation methods
    std::vector<FibonacciResult> calculateFibonacci();
    FibonacciResult calculateFibonacciForRange(double high, double low, 
                                             long long start_time, long long end_time);
    
    // MTF-specific calculations
    std::vector<OHLCBar> aggregateToMTF(const std::vector<OHLCBar>& bars);
    bool isNewMTFPeriod(const OHLCBar& current_bar, const OHLCBar& previous_bar);
    
    // Signal generation
    std::vector<TradingSignal> generateSignals();
    bool checkSignalConditions(double green_level, double red_level, const OHLCBar& bar);
    
    // Take profit and stop loss calculations
    std::pair<double, double> calculateTPLevels(double range_low, double price_range);
    bool checkTPSLConditions(const OHLCBar& bar);
    
    // Utility methods
    double calculatePriceAtLevel(double range_low, double price_range, double fib_level);
    std::vector<FibonacciLevel> getLevelsForRange(double range_low, double price_range);
    
    // Getters
    const std::vector<FibonacciResult>& getFibonacciResults() const { return fibonacci_results_; }
    const std::vector<TradingSignal>& getSignals() const { return signals_; }
    
    // Performance optimization
    void cleanup();
    void reserveCapacity(size_t capacity);
};

// Helper functions for time calculations
long long timeframeToMilliseconds(int timeframe_minutes);
int calculateBarsInTimeframe(int higher_tf_minutes, int current_tf_minutes);
bool isNewTimeframePeriod(long long current_time, long long previous_time, int timeframe_minutes);

} // namespace trading
