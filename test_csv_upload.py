#!/usr/bin/env python3
"""
Test script for CSV upload functionality
"""

import pandas as pd
from main import CSVDataLoader

def test_csv_loader():
    """Test the CSV loader with our sample data"""
    try:
        print("🧪 Testing Multi-Timeframe CSV Data Loader...")
        data = CSVDataLoader.load_csv_file('sample_data.csv')

        print(f"✅ CSV loaded successfully: {len(data)} rows")
        print(f"📊 Columns: {list(data.columns)}")
        print(f"📈 Date range: {data.index[0]} to {data.index[-1]}")
        print(f"💰 Price range: ${data['Low'].min():.2f} - ${data['High'].max():.2f}")
        print(f"📊 Sample data:")
        print(data.head(3))

        # Validate data integrity
        print("\n🔍 Data Validation:")
        print(f"   • No null values: {not data.isnull().any().any()}")
        print(f"   • OHLC relationships valid: {(data['High'] >= data['Low']).all()}")
        print(f"   • High >= Open/Close: {((data['High'] >= data['Open']) & (data['High'] >= data['Close'])).all()}")
        print(f"   • Low <= Open/Close: {((data['Low'] <= data['Open']) & (data['Low'] <= data['Close'])).all()}")
        print(f"   • Volume > 0: {(data['Volume'] > 0).all()}")

        # Test timeframe analysis
        print("\n⏱️  Timeframe Analysis:")
        time_diffs = data.index[1:] - data.index[:-1]
        diff_seconds = [diff.total_seconds() for diff in time_diffs.unique()]
        diff_seconds.sort()

        has_seconds = any(1 <= diff <= 59 for diff in diff_seconds)
        has_minutes = any(60 <= diff <= 3599 for diff in diff_seconds)
        has_hours = any(3600 <= diff <= 86399 for diff in diff_seconds)

        print(f"   • Second-level data: {'✅ Present' if has_seconds else '❌ Missing'}")
        print(f"   • Minute-level data: {'✅ Present' if has_minutes else '❌ Missing'}")
        print(f"   • Hour-level data: {'✅ Present' if has_hours else '❌ Missing'}")
        print(f"   • All intervals (seconds): {[int(d) for d in diff_seconds]}")

        return True

    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    success = test_csv_loader()
    if success:
        print("\n🎉 CSV upload functionality test PASSED!")
    else:
        print("\n💥 CSV upload functionality test FAILED!")
