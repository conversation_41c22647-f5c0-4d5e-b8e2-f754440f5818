#!/usr/bin/env python3
"""
Test script for CSV upload functionality
"""

import pandas as pd
from main import CSVDataLoader

def test_csv_loader():
    """Test the CSV loader with our sample data"""
    try:
        print("🧪 Testing CSV Data Loader...")
        data = CSVDataLoader.load_csv_file('sample_data.csv')
        
        print(f"✅ CSV loaded successfully: {len(data)} rows")
        print(f"📊 Columns: {list(data.columns)}")
        print(f"📈 Date range: {data.index[0]} to {data.index[-1]}")
        print(f"💰 Price range: ${data['Low'].min():.2f} - ${data['High'].max():.2f}")
        print(f"📊 Sample data:")
        print(data.head(3))
        
        # Validate data integrity
        print("\n🔍 Data Validation:")
        print(f"   • No null values: {not data.isnull().any().any()}")
        print(f"   • OHLC relationships valid: {(data['High'] >= data['Low']).all()}")
        print(f"   • High >= Open/Close: {((data['High'] >= data['Open']) & (data['High'] >= data['Close'])).all()}")
        print(f"   • Low <= Open/Close: {((data['Low'] <= data['Open']) & (data['Low'] <= data['Close'])).all()}")
        print(f"   • Volume > 0: {(data['Volume'] > 0).all()}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    success = test_csv_loader()
    if success:
        print("\n🎉 CSV upload functionality test PASSED!")
    else:
        print("\n💥 CSV upload functionality test FAILED!")
