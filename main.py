#!/usr/bin/env python3
"""
Enhanced PyQt6 Candlestick Chart GUI with C++ Fibonacci Calculator
Features:
- High-performance C++ Fibonacci calculations
- Real-time data from Yahoo Finance
- Interactive candlestick charts with Fibonacci levels
- Trading signals and position tracking
- Multi-timeframe support
"""

import sys
import yfinance as yf
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from PyQt6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout,
                            QWidget, QComboBox, QLabel, QPushButton, QLineEdit,
                            QCheckBox, QSplitter, QGroupBox, QGridLayout,
                            QMessageBox, QProgressBar, QStatusBar, QSpinBox,
                            QTabWidget, QTextEdit, QSlider, QFileDialog)
from PyQt6.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt6.QtGui import QFont, QPalette, QColor
import pyqtgraph as pg
from pyqtgraph import PlotWidget

# Import our C++ Fibonacci calculator
try:
    import fibonacci_calc
    CPP_AVAILABLE = True
    print("C++ Fibonacci calculator loaded successfully!")
except ImportError as e:
    CPP_AVAILABLE = False
    print(f"C++ Fibonacci calculator not available: {e}")
    print("Falling back to Python implementation")

from candlestick_gui import CandlestickItem, DataFetcher


class CSVDataLoader:
    """Handles loading and validating CSV data for the trading chart"""

    @staticmethod
    def load_csv_file(file_path):
        """
        Load CSV file and convert to OHLCV format compatible with the chart

        Expected CSV format:
        - Must have columns: Date/Datetime, Open, High, Low, Close, Volume
        - Date column can be named: Date, Datetime, Time, Timestamp
        - OHLCV columns can be case-insensitive
        - Date should be in a parseable format (ISO, US format, etc.)
        - MUST contain data for ALL THREE timeframes: seconds, minutes, and hours

        Returns:
            pandas.DataFrame: Formatted OHLCV data with datetime index
        """
        try:
            # Read CSV file
            df = pd.read_csv(file_path)

            if df.empty:
                raise ValueError("CSV file is empty")

            print(f"📁 CSV loaded: {len(df)} rows, columns: {list(df.columns)}")

            # Normalize column names (case-insensitive)
            df.columns = df.columns.str.strip()

            # Find date/time column
            date_columns = ['date', 'datetime', 'time', 'timestamp']
            date_col = None
            for col in df.columns:
                if col.lower() in date_columns:
                    date_col = col
                    break

            if date_col is None:
                raise ValueError("No date/time column found. Expected one of: Date, Datetime, Time, Timestamp")

            # Find OHLCV columns
            required_columns = ['open', 'high', 'low', 'close', 'volume']
            found_columns = {}

            for req_col in required_columns:
                found = False
                for col in df.columns:
                    if col.lower() == req_col:
                        found_columns[req_col] = col
                        found = True
                        break

                if not found:
                    # Try common variations
                    if req_col == 'volume':
                        for col in df.columns:
                            if col.lower() in ['vol', 'volume', 'v']:
                                found_columns[req_col] = col
                                found = True
                                break

                    if not found:
                        raise ValueError(f"Required column '{req_col}' not found in CSV")

            print(f"📊 Column mapping: {found_columns}")

            # Create new DataFrame with standardized columns
            result_df = pd.DataFrame()

            # Map OHLCV columns with proper numeric conversion first
            for col_name, csv_col in found_columns.items():
                try:
                    numeric_data = pd.to_numeric(df[csv_col], errors='coerce')
                    if numeric_data.isnull().all():
                        raise ValueError(f"Column '{csv_col}' contains no valid numeric data")
                    result_df[col_name.capitalize()] = numeric_data
                except Exception as e:
                    raise ValueError(f"Error converting column '{csv_col}' to numeric: {str(e)}")

            # Parse date column and set as index
            try:
                result_df.index = pd.to_datetime(df[date_col])
            except Exception as e:
                raise ValueError(f"Could not parse date column '{date_col}': {str(e)}")

            # Validate data
            if result_df.isnull().any().any():
                null_counts = result_df.isnull().sum()
                print(f"⚠️ Warning: Found null values: {null_counts.to_dict()}")
                # Drop rows with any null values
                result_df = result_df.dropna()
                print(f"📊 After removing null rows: {len(result_df)} rows remaining")

            if result_df.empty:
                raise ValueError("No valid data rows after cleaning")

            # Validate OHLC relationships
            invalid_ohlc = (
                (result_df['High'] < result_df['Low']) |
                (result_df['High'] < result_df['Open']) |
                (result_df['High'] < result_df['Close']) |
                (result_df['Low'] > result_df['Open']) |
                (result_df['Low'] > result_df['Close'])
            )

            if invalid_ohlc.any():
                invalid_count = invalid_ohlc.sum()
                print(f"⚠️ Warning: Found {invalid_count} rows with invalid OHLC relationships")
                # Remove invalid rows
                result_df = result_df[~invalid_ohlc]
                print(f"📊 After removing invalid OHLC rows: {len(result_df)} rows remaining")

            if result_df.empty:
                raise ValueError("No valid data rows after OHLC validation")

            # Sort by date
            result_df = result_df.sort_index()

            # Verify that data contains all required timeframes (seconds, minutes, hours)
            CSVDataLoader._verify_multi_timeframe_data(result_df)

            print(f"✅ CSV data loaded successfully: {len(result_df)} bars from {result_df.index[0]} to {result_df.index[-1]}")

            return result_df

        except Exception as e:
            print(f"❌ Error loading CSV: {str(e)}")
            raise

    @staticmethod
    def _verify_multi_timeframe_data(df):
        """
        Verify that the CSV data contains all required timeframes: seconds, minutes, and hours

        Args:
            df: DataFrame with datetime index

        Raises:
            ValueError: If any required timeframe is missing
        """
        if len(df) < 2:
            raise ValueError("Insufficient data to determine timeframes (need at least 2 rows)")

        print(f"🔍 Verifying multi-timeframe data requirements...")

        # Calculate time differences between consecutive rows
        time_diffs = df.index[1:] - df.index[:-1]
        unique_diffs = time_diffs.unique()

        print(f"📊 Found {len(unique_diffs)} unique time intervals in data")

        # Convert to seconds for analysis
        diff_seconds = [diff.total_seconds() for diff in unique_diffs]
        diff_seconds.sort()

        print(f"📈 Time intervals (seconds): {diff_seconds}")

        # Check for required timeframes
        has_seconds = any(1 <= diff <= 59 for diff in diff_seconds)
        has_minutes = any(60 <= diff <= 3599 for diff in diff_seconds)
        has_hours = any(3600 <= diff <= 86399 for diff in diff_seconds)

        print(f"⏱️  Timeframe Analysis:")
        print(f"   • Second data (1-59s): {'✅ Found' if has_seconds else '❌ Missing'}")
        print(f"   • Minute data (1-59m): {'✅ Found' if has_minutes else '❌ Missing'}")
        print(f"   • Hour data (1-23h): {'✅ Found' if has_hours else '❌ Missing'}")

        # Collect missing timeframes
        missing_timeframes = []
        if not has_seconds:
            missing_timeframes.append("second-level data (1-59 seconds)")
        if not has_minutes:
            missing_timeframes.append("minute-level data (1-59 minutes)")
        if not has_hours:
            missing_timeframes.append("hour-level data (1-23 hours)")

        if missing_timeframes:
            missing_str = ", ".join(missing_timeframes)
            raise ValueError(
                f"CSV data is missing required timeframes: {missing_str}\n\n"
                f"Your CSV must contain data at ALL THREE timeframe levels:\n"
                f"• Second-level data (intervals of 1-59 seconds)\n"
                f"• Minute-level data (intervals of 1-59 minutes)\n"
                f"• Hour-level data (intervals of 1-23 hours)\n\n"
                f"Current data only contains intervals: {[f'{int(d)}s' for d in diff_seconds]}\n\n"
                f"Please ensure your CSV includes data points with varying time intervals\n"
                f"that span seconds, minutes, and hours."
            )

        print(f"✅ Multi-timeframe verification passed: All required timeframes present")

        # Provide detailed breakdown of detected timeframes
        second_intervals = [d for d in diff_seconds if 1 <= d <= 59]
        minute_intervals = [d for d in diff_seconds if 60 <= d <= 3599]
        hour_intervals = [d for d in diff_seconds if 3600 <= d <= 86399]

        if second_intervals:
            print(f"   📍 Second intervals: {[f'{int(d)}s' for d in second_intervals[:5]]}{'...' if len(second_intervals) > 5 else ''}")
        if minute_intervals:
            print(f"   📍 Minute intervals: {[f'{int(d/60)}m' for d in minute_intervals[:5]]}{'...' if len(minute_intervals) > 5 else ''}")
        if hour_intervals:
            print(f"   📍 Hour intervals: {[f'{int(d/3600)}h' for d in hour_intervals[:5]]}{'...' if len(hour_intervals) > 5 else ''}")

        return True


class MTFPositioningHelper:
    """Helper class for consistent MTF positioning across candles and indicators"""

    @staticmethod
    def calculate_mtf_positioning(mtf_index, mtf_period_minutes, base_timeframe_minutes, encompass_mode=False):
        """Calculate consistent MTF positioning for both candles and indicators"""
        bars_per_mtf = mtf_period_minutes / base_timeframe_minutes
        x_start = mtf_index * bars_per_mtf
        x_center = x_start + bars_per_mtf / 2

        if encompass_mode:
            # For MTF candles: span the full width to encompass base candles
            span_width = bars_per_mtf
            encompass_start = x_start - 0.4  # Start slightly before first base candle
            encompass_end = x_start + bars_per_mtf - 0.6  # End slightly before last base candle
            line_start = encompass_start
            line_end = encompass_end
        else:
            # For Fibonacci lines: use 80% width for cleaner appearance
            span_width = bars_per_mtf * 0.8
            line_start = x_center - span_width/2
            line_end = x_center + span_width/2
            encompass_start = line_start
            encompass_end = line_end

        result = {
            'bars_per_mtf': bars_per_mtf,
            'x_start': x_start,
            'x_center': x_center,
            'span_width': span_width,
            'line_start': line_start,
            'line_end': line_end,
            'encompass_start': encompass_start,
            'encompass_end': encompass_end
        }

        # Debug output for first few calculations (extra detail for 5m)
        if mtf_index < 3:
            mode_str = "ENCOMPASS" if encompass_mode else "FIBONACCI"
            print(f"   🎯 MTF Positioning {mtf_index} ({mode_str}): MTF={mtf_period_minutes}m, Base={base_timeframe_minutes}m")
            print(f"      bars_per_mtf={bars_per_mtf:.1f}, x_center={x_center:.1f}, span={span_width:.1f}")

            # Extra debugging for 5-minute periods
            if mtf_period_minutes == 5:
                print(f"      🔍 5m DEBUG: x_start={x_start:.1f}, line_start={result['line_start']:.1f}, line_end={result['line_end']:.1f}")

        return result

    @staticmethod
    def calculate_timestamp_aligned_positioning(mtf_timestamp, base_data_timestamps, mtf_period_minutes, base_timeframe_minutes):
        """Calculate MTF positioning based on actual timestamp alignment with base data"""
        try:
            # Validate inputs
            if not base_data_timestamps or len(base_data_timestamps) == 0:
                print("Warning: Empty base data timestamps - using fallback positioning")
                bars_per_mtf = mtf_period_minutes / base_timeframe_minutes
                return {
                    'x_center': bars_per_mtf / 2,
                    'span_width': bars_per_mtf,  # Full width to encompass base candles
                    'line_start': 0,
                    'line_end': bars_per_mtf,
                    'base_index': None,
                    'bars_per_mtf': bars_per_mtf,
                    'encompass_start': 0,
                    'encompass_end': bars_per_mtf
                }

            # Find the index in base data that corresponds to this MTF timestamp
            # MTF timestamp should align with the start of the MTF period
            base_index = None

            # Convert timestamps to comparable format
            if hasattr(mtf_timestamp, 'timestamp'):
                mtf_ts = mtf_timestamp.timestamp()
            elif hasattr(mtf_timestamp, 'value'):
                # Handle pandas Timestamp
                mtf_ts = mtf_timestamp.value / 1e9  # Convert nanoseconds to seconds
            else:
                mtf_ts = float(mtf_timestamp)

            # Find the closest base data timestamp that matches the MTF period start
            min_diff = float('inf')
            for i, base_ts in enumerate(base_data_timestamps):
                try:
                    if hasattr(base_ts, 'timestamp'):
                        base_ts_val = base_ts.timestamp()
                    elif hasattr(base_ts, 'value'):
                        # Handle pandas Timestamp
                        base_ts_val = base_ts.value / 1e9  # Convert nanoseconds to seconds
                    else:
                        base_ts_val = float(base_ts)

                    # Calculate time difference
                    diff = abs(base_ts_val - mtf_ts)
                    if diff < min_diff:
                        min_diff = diff
                        base_index = i

                    # If we find an exact match or very close match, use it
                    if diff < 60:  # Within 60 seconds tolerance (increased for robustness)
                        break

                except (ValueError, TypeError, AttributeError) as e:
                    # Skip problematic timestamps
                    continue

            if base_index is None:
                print(f"Warning: Could not find matching timestamp for MTF {mtf_timestamp} - using fallback")
                # Fallback to simple calculation
                bars_per_mtf = mtf_period_minutes / base_timeframe_minutes
                return {
                    'x_center': bars_per_mtf / 2,
                    'span_width': bars_per_mtf,  # Full width to encompass base candles
                    'line_start': 0,
                    'line_end': bars_per_mtf,
                    'base_index': None,
                    'bars_per_mtf': bars_per_mtf,
                    'encompass_start': 0,
                    'encompass_end': bars_per_mtf
                }

            # Calculate positioning to encompass the base candles
            bars_per_mtf = mtf_period_minutes / base_timeframe_minutes

            # MTF candle should encompass exactly the base candles it represents
            encompass_start = base_index - 0.4  # Start slightly before first base candle
            encompass_end = base_index + bars_per_mtf - 0.6  # End slightly before last base candle

            # Calculate center and width to encompass the base candles
            x_center = (encompass_start + encompass_end) / 2
            span_width = encompass_end - encompass_start

            # Ensure positioning is within reasonable bounds
            if x_center < 0:
                x_center = bars_per_mtf / 2
                base_index = 0
                encompass_start = 0
                encompass_end = bars_per_mtf
                span_width = bars_per_mtf
            elif x_center > len(base_data_timestamps) + bars_per_mtf:
                x_center = len(base_data_timestamps) - bars_per_mtf / 2
                base_index = len(base_data_timestamps) - int(bars_per_mtf)
                encompass_start = base_index
                encompass_end = base_index + bars_per_mtf
                span_width = bars_per_mtf

            result = {
                'x_center': x_center,
                'span_width': span_width,
                'line_start': encompass_start,
                'line_end': encompass_end,
                'base_index': base_index,
                'bars_per_mtf': bars_per_mtf,
                'encompass_start': encompass_start,
                'encompass_end': encompass_end
            }

            return result

        except Exception as e:
            print(f"Error in timestamp-aligned positioning: {e}")
            import traceback
            traceback.print_exc()
            # Fallback to simple calculation
            bars_per_mtf = mtf_period_minutes / base_timeframe_minutes
            return {
                'x_center': bars_per_mtf / 2,
                'span_width': bars_per_mtf,
                'line_start': 0,
                'line_end': bars_per_mtf,
                'base_index': None,
                'bars_per_mtf': bars_per_mtf,
                'encompass_start': 0,
                'encompass_end': bars_per_mtf
            }


class MTFCandleItem(pg.GraphicsObject):
    """Multi-timeframe candle overlay item - TradingView style"""

    def __init__(self, mtf_data, base_data, mtf_period_minutes, base_timeframe_minutes=1, transparency=60):
        pg.GraphicsObject.__init__(self)
        self.mtf_data = mtf_data
        self.base_data = base_data
        self.mtf_period_minutes = mtf_period_minutes
        self.base_timeframe_minutes = base_timeframe_minutes
        self.transparency = transparency

        print(f"Creating MTF candles: {len(mtf_data)} MTF candles over {len(base_data)} base bars")
        print(f"MTF period: {mtf_period_minutes}m, Base TF: {base_timeframe_minutes}m, Transparency: {transparency}%")

        self.generatePicture()

    def generatePicture(self):
        self.picture = pg.QtGui.QPicture()
        p = pg.QtGui.QPainter(self.picture)

        if len(self.mtf_data) == 0:
            p.end()
            return

        # Traditional MTF candle colors (green/red)
        bull_pen = pg.mkPen(color='#00FF00', width=3)  # Bright green
        bear_pen = pg.mkPen(color='#FF0000', width=3)  # Bright red

        # Use transparency setting (convert percentage to 0-255 range)
        alpha = int((self.transparency / 100.0) * 255)
        bull_brush = pg.mkBrush(color=pg.QtGui.QColor(0, 255, 0, alpha))  # Green with custom transparency
        bear_brush = pg.mkBrush(color=pg.QtGui.QColor(255, 0, 0, alpha))  # Red with custom transparency

        print(f"Drawing {len(self.mtf_data)} MTF candles")

        for i, (timestamp, row) in enumerate(self.mtf_data.iterrows()):
            try:
                # Handle different column name formats
                if 'Open' in row:
                    open_price = float(row['Open'])
                    high_price = float(row['High'])
                    low_price = float(row['Low'])
                    close_price = float(row['Close'])
                elif 'open' in row:
                    open_price = float(row['open'])
                    high_price = float(row['high'])
                    low_price = float(row['low'])
                    close_price = float(row['close'])
                else:
                    # Try index-based access
                    open_price = float(row.iloc[0])
                    high_price = float(row.iloc[1])
                    low_price = float(row.iloc[2])
                    close_price = float(row.iloc[3])

                # Determine if bullish or bearish
                is_bullish = close_price >= open_price
                pen = bull_pen if is_bullish else bear_pen
                brush = bull_brush if is_bullish else bear_brush

                # Use timestamp-aligned MTF positioning for better accuracy
                if hasattr(self.base_data, 'index'):
                    base_timestamps = self.base_data.index.tolist()
                    pos = MTFPositioningHelper.calculate_timestamp_aligned_positioning(
                        timestamp, base_timestamps, self.mtf_period_minutes, self.base_timeframe_minutes
                    )
                    if i < 3:  # Debug first few candles
                        print(f"MTF candle {i} (timestamp-aligned): base_index={pos.get('base_index', 'N/A')}, encompass={pos['encompass_start']:.1f}-{pos['encompass_end']:.1f}")
                else:
                    # Fallback to simple positioning with encompass mode
                    pos = MTFPositioningHelper.calculate_mtf_positioning(
                        i, self.mtf_period_minutes, self.base_timeframe_minutes, encompass_mode=True
                    )
                    if i < 3:  # Debug first few candles
                        print(f"MTF candle {i} (fallback): encompass={pos['encompass_start']:.1f}-{pos['encompass_end']:.1f}")

                # Use encompassing positioning to span the base candles
                candle_left = pos['encompass_start']
                candle_right = pos['encompass_end']
                candle_width = candle_right - candle_left
                x_center = (candle_left + candle_right) / 2

                if i < 3:  # Debug first few candles
                    print(f"MTF candle {i}: left={candle_left:.1f}, right={candle_right:.1f}, width={candle_width:.1f}, OHLC=({open_price:.2f},{high_price:.2f},{low_price:.2f},{close_price:.2f})")
                    print(f"   -> This MTF candle encompasses {pos['bars_per_mtf']:.0f} base timeframe candles")

                # Draw high-low line (wick) - TradingView style
                p.setPen(pen)
                p.drawLine(pg.QtCore.QPointF(x_center, low_price), pg.QtCore.QPointF(x_center, high_price))

                # Draw body rectangle that encompasses the base candles
                body_height = abs(close_price - open_price)
                body_bottom = min(open_price, close_price)

                if body_height > 0.001:  # Only draw body if there's meaningful height
                    p.setPen(pen)
                    p.setBrush(brush)
                    # Use the full encompassing width
                    rect = pg.QtCore.QRectF(candle_left, body_bottom, candle_width, body_height)
                    p.drawRect(rect)
                else:
                    # Doji candle - draw a horizontal line across the full width
                    p.setPen(pg.mkPen(color=pen.color(), width=2))
                    p.drawLine(pg.QtCore.QPointF(candle_left, open_price),
                              pg.QtCore.QPointF(candle_right, open_price))

                # TradingView-style: Draw outline borders for better visibility
                outline_pen = pg.mkPen(color='white', width=1)
                p.setPen(outline_pen)

                # Draw border around the body using full encompassing width
                if body_height > 0.001:
                    border_rect = pg.QtCore.QRectF(candle_left, body_bottom, candle_width, body_height)
                    p.drawRect(border_rect)

            except Exception as e:
                print(f"Error drawing MTF candle {i}: {e}")
                continue

        p.end()
        print(f"MTF candle generation complete")

    def paint(self, p, *args):
        p.drawPicture(0, 0, self.picture)

    def boundingRect(self):
        return pg.QtCore.QRectF(self.picture.boundingRect())


class PythonFibonacciCalculator:
    """Fallback Python implementation of Fibonacci calculator"""

    def __init__(self):
        self.fib_levels = [1.1, 1.08, 1.0, 0.9, 0.1, 0.0, -0.08, -0.1]
        self.results = []
        self.signals = []
        self.mtf_mode = False
        self.mtf_period = 5
        self.current_tf_period = 1
        self._cached_mtf_data = None
        self._last_data_hash = None
    
    def set_mtf_mode(self, enabled, mtf_period=5, current_tf_period=1):
        self.mtf_mode = enabled
        self.mtf_period = mtf_period  # MTF period in minutes
        self.current_tf_period = current_tf_period  # Current timeframe in minutes
        print(f"MTF Mode: {enabled}, MTF Period: {mtf_period}min, Current TF: {current_tf_period}min")

    def set_data(self, ohlc_data):
        """Convert pandas DataFrame to internal format"""
        # Calculate data hash to detect changes
        import hashlib
        data_str = f"{len(ohlc_data)}_{ohlc_data.index[0] if not ohlc_data.empty else 'empty'}_{ohlc_data.index[-1] if not ohlc_data.empty else 'empty'}"
        new_data_hash = hashlib.md5(data_str.encode()).hexdigest()

        # Check if data actually changed
        if new_data_hash != self._last_data_hash:
            print(f"🔄 Data changed - invalidating MTF cache (hash: {new_data_hash[:8]}...)")
            self._cached_mtf_data = None
            self._last_data_hash = new_data_hash
        else:
            print(f"📊 Data unchanged - keeping MTF cache")

        self.data = []
        self.original_data = ohlc_data.copy()

        for idx, row in ohlc_data.iterrows():
            timestamp = int(idx.timestamp() * 1000) if hasattr(idx, 'timestamp') else int(idx.value // 1000000)
            self.data.append({
                'open': row['Open'],
                'high': row['High'],
                'low': row['Low'],
                'close': row['Close'],
                'volume': row['Volume'],
                'timestamp': timestamp,
                'datetime': idx
            })

        print(f"Loaded {len(self.data)} bars for Fibonacci calculation")

    def aggregate_to_mtf(self):
        """Aggregate current timeframe data to MTF periods"""
        if not self.mtf_mode or not self.data:
            return self.data

        # Check if we can use cached MTF data
        cache_key = f"{self.mtf_period}_{len(self.data)}_{self._last_data_hash}"
        if self._cached_mtf_data is not None and hasattr(self, '_cache_key') and self._cache_key == cache_key:
            print(f"📊 Using cached MTF data: {len(self._cached_mtf_data)} bars")
            return self._cached_mtf_data

        print(f"🔄 Aggregating to MTF: {self.mtf_period} minutes (cache miss)")

        # Convert MTF period to pandas frequency
        freq_map = {
            1: '1min',
            5: '5min',
            15: '15min',
            30: '30min',
            60: '1h',
            240: '4h',
            1440: '1D'
        }
        freq = freq_map.get(self.mtf_period, f'{self.mtf_period}min')

        # Create DataFrame from original data for resampling
        df = self.original_data.copy()

        # Ensure the index is timezone-aware if needed
        if df.index.tz is None:
            df.index = df.index.tz_localize('UTC')

        try:
            # Resample to MTF with proper aggregation
            mtf_data = df.resample(freq, label='left', closed='left').agg({
                'Open': 'first',
                'High': 'max',
                'Low': 'min',
                'Close': 'last',
                'Volume': 'sum'
            }).dropna()

            print(f"MTF aggregation: {len(df)} -> {len(mtf_data)} bars using frequency {freq}")

            # Convert back to internal format
            mtf_bars = []
            for idx, row in mtf_data.iterrows():
                timestamp = int(idx.timestamp() * 1000) if hasattr(idx, 'timestamp') else int(idx.value // 1000000)
                mtf_bars.append({
                    'open': row['Open'],
                    'high': row['High'],
                    'low': row['Low'],
                    'close': row['Close'],
                    'volume': row['Volume'],
                    'timestamp': timestamp,
                    'datetime': idx
                })

            # Cache the results for future use
            self._cached_mtf_data = mtf_bars
            self._cache_key = f"{self.mtf_period}_{len(self.data)}_{self._last_data_hash}"
            print(f"💾 Cached MTF data: {len(mtf_bars)} bars")

            return mtf_bars

        except Exception as e:
            print(f"Error in MTF aggregation: {e}")
            print("Falling back to original data")
            return self.data

    def calculate_fibonacci(self):
        """Calculate Fibonacci levels - MTF aware"""
        self.results = []

        if not self.data:
            return self.results

        # Use MTF data if enabled, otherwise use regular data
        if self.mtf_mode:
            working_data = self.aggregate_to_mtf()
            print(f"Using MTF data: {len(working_data)} MTF bars")
        else:
            working_data = self.data
            print(f"Using regular data: {len(working_data)} bars")

        # Calculate Fibonacci for each bar/MTF period
        for i, bar in enumerate(working_data):
            if bar['high'] > bar['low']:
                price_range = bar['high'] - bar['low']

                # Debug logging for first 5 bars
                if i < 5:
                    print(f"   🔢 Fib Calc {i}: high={bar['high']:.2f}, low={bar['low']:.2f}, range={price_range:.2f}")

                levels = []
                for fib_level in self.fib_levels:
                    price = bar['low'] + (price_range * fib_level)
                    levels.append({'level': fib_level, 'price': price})

                result = {
                    'levels': levels,
                    'range_high': bar['high'],
                    'range_low': bar['low'],
                    'price_range': price_range,
                    'green_level': bar['low'] + (price_range * 1.0),
                    'red_level': bar['low'] + (price_range * 0.0),
                    'level_0_9': bar['low'] + (price_range * 0.9),  # 0.9 level for long SL
                    'level_0_1': bar['low'] + (price_range * 0.1),  # 0.1 level for short SL
                    'is_bullish': bar['close'] > bar['open'],
                    'bar_index': i,
                    'timestamp': bar['timestamp'],
                    'is_mtf': self.mtf_mode
                }
                self.results.append(result)

                # Debug first few results
                if i < 3:
                    print(f"      Green level: {result['green_level']:.2f}, Red level: {result['red_level']:.2f}")
            else:
                print(f"   ⚠️ Skipping bar {i}: high={bar['high']:.2f} <= low={bar['low']:.2f}")

        print(f"Generated {len(self.results)} Fibonacci results")
        return self.results
    
    def generate_signals(self):
        """Generate trading signals based on Fibonacci levels - TradingView compatible"""
        self.signals = []

        if not self.results or not self.data:
            print("No Fibonacci results or data available for signal generation")
            return self.signals

        # Initialize signal tracking state
        green_signal_triggered = False
        red_signal_triggered = False
        active_buy_position = False
        active_sell_position = False
        buy_tp_level = None
        sell_tp_level = None
        buy_sl_level = None
        sell_sl_level = None

        print(f"🎯 SIGNAL GENERATION START: {len(self.data)} bars, {len(self.results)} Fibonacci results")

        # Use MTF data if enabled, otherwise use regular data
        if self.mtf_mode:
            working_data = self.aggregate_to_mtf()
            print(f"Using MTF data for signals: {len(working_data)} MTF bars")
        else:
            working_data = self.data
            print(f"Using regular data for signals: {len(working_data)} bars")

        # For proper signal positioning, we need to check signals against the base data
        # but use Fibonacci levels from MTF calculations
        last_fib_result = None

        # Track previous Fibonacci levels for delayed signals (matching PineScript logic)
        # This implements the exact PineScript behavior from lines 342-346
        prev_green_level = None
        prev_red_level = None
        prev_level_0_9 = None  # For long stop loss
        prev_level_0_1 = None  # For short stop loss
        prev_range_low = None
        prev_price_range = None
        current_fib_index = -1
        htf_just_closed = False

        for i, bar in enumerate(self.data):  # Always use base data for signal checking
            bar_timestamp = bar['timestamp']

            # Find which Fibonacci period this bar belongs to (matching PineScript MTF logic)
            bar_fib_index = -1
            for j, fib_result in enumerate(self.results):
                fib_timestamp = fib_result.get('timestamp', 0)
                if bar_timestamp >= fib_timestamp:
                    bar_fib_index = j

            # Check if we've moved to a new Fibonacci period (HTF period closed)
            # This matches PineScript lines 342-346 where prev_levels are stored
            if bar_fib_index != current_fib_index and bar_fib_index >= 0:
                if current_fib_index >= 0:
                    # Store the PREVIOUS completed period's Fibonacci levels (PineScript behavior)
                    prev_fib = self.results[current_fib_index]
                    prev_green_level = prev_fib.get('green_level')
                    prev_red_level = prev_fib.get('red_level')
                    prev_level_0_9 = prev_fib.get('level_0_9')  # For long stop loss
                    prev_level_0_1 = prev_fib.get('level_0_1')  # For short stop loss
                    prev_range_low = prev_fib.get('range_low')
                    prev_price_range = prev_fib.get('price_range')
                    htf_just_closed = True
                    if i < 10:  # Debug first few
                        print(f"📊 HTF Period Closed at bar {i}: Storing Fib {current_fib_index} levels (Green={prev_green_level:.2f}, Red={prev_red_level:.2f}, 0.9={prev_level_0_9:.2f}, 0.1={prev_level_0_1:.2f})")
                current_fib_index = bar_fib_index

            # Use PREVIOUS completed HTF period's Fibonacci levels for signals (exact PineScript logic)
            if prev_green_level is None or prev_red_level is None:
                # Skip until we have previous completed HTF period levels to use
                continue

            # Use the stored previous levels (not current ones!)
            green_level = prev_green_level
            red_level = prev_red_level
            range_low = prev_range_low
            price_range = prev_price_range

            # Signal reset logic matching PineScript behavior
            if self.mtf_mode:
                # MTF Mode: Reset signals only when new MTF period starts (htf_just_closed)
                if htf_just_closed:
                    green_signal_triggered = False
                    red_signal_triggered = False
                    htf_just_closed = False  # Reset flag after using it
                    if i < 10:  # Debug first few
                        print(f"📊 MTF Mode: HTF period closed at bar {i} - resetting signal triggers")
            else:
                # Legacy Mode: Reset signals every bar to allow new signals (TradingView behavior)
                green_signal_triggered = False
                red_signal_triggered = False
                if i < 5:  # Only log first few for debugging
                    print(f"📊 Legacy Mode: Resetting signal triggers at bar {i}")

            # Levels are already set above from previous HTF period (PineScript logic)

            if green_level is None or red_level is None:
                continue

            # Check for buy signal (price hits green level) - PineScript logic
            if (not green_signal_triggered and
                bar['high'] >= green_level and
                bar['low'] <= green_level):

                signal = {
                    'type': 'BUY',
                    'price': green_level,
                    'timestamp': bar_timestamp,
                    'bar_index': i  # Use base data index for proper chart positioning
                }
                self.signals.append(signal)
                green_signal_triggered = True

                # Update position tracking for buy signal (PineScript behavior)
                active_buy_position = True
                active_sell_position = False
                # Reset opposite position TP/SL levels
                sell_tp_level = None
                sell_sl_level = None

                # Calculate TP and SL levels for new buy position
                if range_low is not None and price_range is not None:
                    buy_tp_level = range_low + (price_range * 1.1)   # 1.1 level for buy TP
                    buy_sl_level = prev_level_0_9  # 0.9 level for long stop loss

                    # Check if TP is hit on the same bar as the buy signal (PineScript behavior)
                    if bar['high'] >= buy_tp_level and bar['low'] <= buy_tp_level:
                        tp_signal = {
                            'type': 'TAKE_PROFIT',
                            'price': buy_tp_level,
                            'timestamp': bar_timestamp,
                            'bar_index': i
                        }
                        self.signals.append(tp_signal)
                        active_buy_position = False
                        buy_tp_level = None
                        buy_sl_level = None
                        print(f"💰 BUY TP hit on same bar {i}: price={tp_signal['price']:.2f}")
                    # Check if SL is hit on the same bar as the buy signal
                    elif bar['low'] <= buy_sl_level and bar['high'] >= buy_sl_level:
                        sl_signal = {
                            'type': 'STOP_LOSS',
                            'price': buy_sl_level,
                            'timestamp': bar_timestamp,
                            'bar_index': i
                        }
                        self.signals.append(sl_signal)
                        active_buy_position = False
                        buy_tp_level = None
                        buy_sl_level = None
                        print(f"🛑 BUY SL hit on same bar {i}: price={sl_signal['price']:.2f}")

                tp_str = f"{buy_tp_level:.2f}" if buy_tp_level is not None else "N/A"
                sl_str = f"{buy_sl_level:.2f}" if buy_sl_level is not None else "N/A"
                print(f"🟢 BUY signal at bar {i}: price={green_level:.2f}, TP={tp_str}, SL={sl_str}")

            # Check for sell signal (price hits red level) - PineScript logic
            if (not red_signal_triggered and
                bar['low'] <= red_level and
                bar['high'] >= red_level):

                signal = {
                    'type': 'SELL',
                    'price': red_level,
                    'timestamp': bar_timestamp,
                    'bar_index': i  # Use base data index for proper chart positioning
                }
                self.signals.append(signal)
                red_signal_triggered = True

                # Update position tracking for sell signal (PineScript behavior)
                active_sell_position = True
                active_buy_position = False
                # Reset opposite position TP/SL levels
                buy_tp_level = None
                buy_sl_level = None

                # Calculate TP and SL levels for new sell position
                if range_low is not None and price_range is not None:
                    sell_tp_level = range_low + (price_range * -0.1)  # -0.1 level for sell TP
                    sell_sl_level = prev_level_0_1  # 0.1 level for short stop loss

                    # Check if TP is hit on the same bar as the sell signal (PineScript behavior)
                    if bar['low'] <= sell_tp_level and bar['high'] >= sell_tp_level:
                        tp_signal = {
                            'type': 'TAKE_PROFIT',
                            'price': sell_tp_level,
                            'timestamp': bar_timestamp,
                            'bar_index': i
                        }
                        self.signals.append(tp_signal)
                        active_sell_position = False
                        sell_tp_level = None
                        sell_sl_level = None
                        print(f"💰 SELL TP hit on same bar {i}: price={tp_signal['price']:.2f}")
                    # Check if SL is hit on the same bar as the sell signal
                    elif bar['high'] >= sell_sl_level and bar['low'] <= sell_sl_level:
                        sl_signal = {
                            'type': 'STOP_LOSS',
                            'price': sell_sl_level,
                            'timestamp': bar_timestamp,
                            'bar_index': i
                        }
                        self.signals.append(sl_signal)
                        active_sell_position = False
                        sell_tp_level = None
                        sell_sl_level = None
                        print(f"🛑 SELL SL hit on same bar {i}: price={sl_signal['price']:.2f}")

                tp_str = f"{sell_tp_level:.2f}" if sell_tp_level is not None else "N/A"
                sl_str = f"{sell_sl_level:.2f}" if sell_sl_level is not None else "N/A"
                print(f"🔴 SELL signal at bar {i}: price={red_level:.2f}, TP={tp_str}, SL={sl_str}")

            # Check for TP/SL conditions first (PineScript behavior - checked on every bar)
            # Check buy TP
            if (active_buy_position and buy_tp_level is not None and
                bar['high'] >= buy_tp_level and bar['low'] <= buy_tp_level):

                signal = {
                    'type': 'TAKE_PROFIT',
                    'price': buy_tp_level,
                    'timestamp': bar_timestamp,
                    'bar_index': i  # Use base data index for proper chart positioning
                }
                self.signals.append(signal)
                # Close all positions when TP is hit (PineScript behavior)
                active_buy_position = False
                active_sell_position = False
                buy_tp_level = None
                sell_tp_level = None
                buy_sl_level = None
                sell_sl_level = None
                print(f"💰 BUY TP hit at bar {i}: price={signal['price']:.2f}")

            # Check buy SL (only if TP not hit)
            elif (active_buy_position and buy_sl_level is not None and
                  bar['low'] <= buy_sl_level and bar['high'] >= buy_sl_level):

                signal = {
                    'type': 'STOP_LOSS',
                    'price': buy_sl_level,
                    'timestamp': bar_timestamp,
                    'bar_index': i  # Use base data index for proper chart positioning
                }
                self.signals.append(signal)
                # Close all positions when SL is hit (PineScript behavior)
                active_buy_position = False
                active_sell_position = False
                buy_tp_level = None
                sell_tp_level = None
                buy_sl_level = None
                sell_sl_level = None
                print(f"🛑 BUY SL hit at bar {i}: price={signal['price']:.2f}")

            # Check sell TP
            if (active_sell_position and sell_tp_level is not None and
                bar['low'] <= sell_tp_level and bar['high'] >= sell_tp_level):

                signal = {
                    'type': 'TAKE_PROFIT',
                    'price': sell_tp_level,
                    'timestamp': bar_timestamp,
                    'bar_index': i  # Use base data index for proper chart positioning
                }
                self.signals.append(signal)
                # Close all positions when TP is hit (PineScript behavior)
                active_buy_position = False
                active_sell_position = False
                buy_tp_level = None
                sell_tp_level = None
                buy_sl_level = None
                sell_sl_level = None
                print(f"💰 SELL TP hit at bar {i}: price={signal['price']:.2f}")

            # Check sell SL (only if TP not hit)
            elif (active_sell_position and sell_sl_level is not None and
                  bar['high'] >= sell_sl_level and bar['low'] <= sell_sl_level):

                signal = {
                    'type': 'STOP_LOSS',
                    'price': sell_sl_level,
                    'timestamp': bar_timestamp,
                    'bar_index': i  # Use base data index for proper chart positioning
                }
                self.signals.append(signal)
                # Close all positions when SL is hit (PineScript behavior)
                active_buy_position = False
                active_sell_position = False
                buy_tp_level = None
                sell_tp_level = None
                buy_sl_level = None
                sell_sl_level = None
                print(f"🛑 SELL SL hit at bar {i}: price={signal['price']:.2f}")

        print(f"🎯 SIGNAL GENERATION COMPLETE: Generated {len(self.signals)} signals")
        for i, signal in enumerate(self.signals):
            print(f"   Signal {i+1}: {signal['type']} at {signal['price']:.2f} (bar {signal['bar_index']})")

        return self.signals


class FibonacciOverlay:
    """Handles drawing Fibonacci levels on the chart"""
    
    def __init__(self, chart_widget):
        self.chart = chart_widget
        self.fib_lines = []
        self.signal_items = []
        
        # Fibonacci colors matching Pine Script
        self.fib_colors = {
            1.1: 'white',
            1.08: 'white', 
            1.0: 'green',
            0.0: 'red',
            -0.08: 'white',
            -0.1: 'white'
        }
    
    def clear_fibonacci(self):
        """Clear all Fibonacci overlays"""
        for line in self.fib_lines:
            self.chart.removeItem(line)
        for signal in self.signal_items:
            self.chart.removeItem(signal)

        self.fib_lines.clear()
        self.signal_items.clear()
    
    def draw_fibonacci_levels(self, fibonacci_results, max_bars_display=50, mtf_period_minutes=None, base_timeframe_minutes=1, base_data=None):
        """Draw Fibonacci levels on the chart - TradingView style with proper MTF positioning"""
        self.clear_fibonacci()

        print(f"\n🎨 FIBONACCI DRAWING DEBUG:")
        print(f"   fibonacci_results count: {len(fibonacci_results) if fibonacci_results else 0}")
        print(f"   max_bars_display: {max_bars_display}")
        print(f"   mtf_period_minutes: {mtf_period_minutes}")
        print(f"   base_timeframe_minutes: {base_timeframe_minutes}")
        print(f"   base_data provided: {base_data is not None}")

        if not fibonacci_results:
            print("   ❌ No Fibonacci results to display")
            return

        # Limit the number of results to display for performance
        recent_results = fibonacci_results[-max_bars_display:] if len(fibonacci_results) > max_bars_display else fibonacci_results
        print(f"   Drawing {len(recent_results)} Fibonacci result sets")

        # Get base data timestamps for alignment if available
        base_timestamps = None
        if base_data is not None and hasattr(base_data, 'index'):
            base_timestamps = base_data.index.tolist()
            print(f"   Base data timestamps: {len(base_timestamps)} available")

        for i, result in enumerate(recent_results):
            if CPP_AVAILABLE:
                # C++ result format
                bar_index = getattr(result, 'bar_index', i)
                levels = result.levels
                is_mtf = getattr(result, 'is_mtf', False)
                timestamp = getattr(result, 'timestamp', None)
            else:
                # Python result format
                bar_index = result.get('bar_index', i)
                levels = result.get('levels', [])
                is_mtf = result.get('is_mtf', False)
                timestamp = result.get('timestamp', None)

            # Calculate proper positioning using timestamp alignment for MTF
            if is_mtf and mtf_period_minutes and base_timeframe_minutes and base_timestamps:
                # Use timestamp-aligned positioning for better accuracy
                mtf_datetime = None
                if 'datetime' in result:
                    mtf_datetime = result['datetime']
                elif timestamp:
                    # Convert timestamp to datetime if needed
                    import datetime as dt
                    if isinstance(timestamp, (int, float)):
                        mtf_datetime = dt.datetime.fromtimestamp(timestamp / 1000 if timestamp > 1e10 else timestamp)

                if mtf_datetime:
                    pos = MTFPositioningHelper.calculate_timestamp_aligned_positioning(
                        mtf_datetime, base_timestamps, mtf_period_minutes, base_timeframe_minutes
                    )
                    print(f"   📊 Timestamp-aligned MTF Fib {i}: base_index={pos.get('base_index', 'N/A')}, x_center={pos['x_center']:.1f}")
                else:
                    # Fallback to simple positioning
                    pos = MTFPositioningHelper.calculate_mtf_positioning(
                        i, mtf_period_minutes, base_timeframe_minutes
                    )
                    print(f"   📊 Fallback MTF Fib {i}: x_center={pos['x_center']:.1f}")

                line_start = pos['line_start']
                line_end = pos['line_end']
                line_width = 1  # Thin lines for cleaner appearance

                if i < 3:  # Only log first 3 for brevity
                    print(f"   📊 MTF Fib {i}: x_center={pos['x_center']:.1f}, line_start={line_start:.1f}, line_end={line_end:.1f}")
                    # Log the first few Fibonacci levels for this bar
                    for level_data in levels[:2]:  # First 2 levels
                        if CPP_AVAILABLE:
                            fib_level = level_data.level
                            price = level_data.price
                        else:
                            fib_level = level_data['level']
                            price = level_data['price']
                        print(f"      Level {fib_level}: price={price:.2f}")
            else:
                # Regular Fibonacci positioning - individual bar timeframe
                line_width = 1  # Thin lines for cleaner appearance
                line_span = 0.8  # Standard bar width
                line_start = bar_index - line_span/2
                line_end = bar_index + line_span/2

                if i < 3:  # Only log first 3 for brevity
                    print(f"   📊 Regular Fib {i}: bar_index={bar_index}, line_start={line_start:.1f}, line_end={line_end:.1f}")

            # Draw horizontal lines for each Fibonacci level
            for level_data in levels:
                if CPP_AVAILABLE:
                    fib_level = level_data.level
                    price = level_data.price
                else:
                    fib_level = level_data['level']
                    price = level_data['price']

                color = self.fib_colors.get(fib_level, 'gray')

                # Create horizontal line with proper positioning
                line = pg.PlotDataItem(
                    x=[line_start, line_end],
                    y=[price, price],
                    pen=pg.mkPen(color=color, width=line_width, style=Qt.PenStyle.SolidLine)
                )
                self.chart.addItem(line)
                self.fib_lines.append(line)

                # Labels removed for cleaner appearance

        print(f"Drew {len(self.fib_lines)} Fibonacci elements (TradingView style with consistent MTF positioning)")
    
    def draw_signals(self, signals, max_bars_display=None, total_bars=None, mtf_mode=False, mtf_results_count=None, mtf_period=5):
        """Draw trading signals on the chart, respecting max_bars_display setting for MTF periods"""
        if not signals:
            return

        # Filter signals based on MTF periods or base bars
        if max_bars_display is not None:
            if mtf_mode and mtf_results_count is not None:
                # MTF Mode: Filter based on MTF periods, not base bars
                # Calculate which MTF periods should be displayed
                start_mtf_period = max(0, mtf_results_count - max_bars_display)

                # Filter signals to only include those from the displayed MTF periods
                # We need to map signal timestamps to MTF periods
                visible_signals = []
                for signal in signals:
                    signal_bar_index = signal.get('bar_index', 0) if not CPP_AVAILABLE else signal.bar_index

                    # Calculate which MTF period this signal belongs to
                    # Use the actual MTF period setting (e.g., 5 for 5-minute periods)
                    mtf_period_index = signal_bar_index // mtf_period

                    if mtf_period_index >= start_mtf_period:
                        visible_signals.append(signal)

                print(f"📊 MTF Signal Display Filter: Showing {len(visible_signals)} of {len(signals)} signals (MTF periods {start_mtf_period}-{mtf_results_count-1}, {mtf_period}min periods)")
            else:
                # Legacy Mode: Filter based on base timeframe bars
                start_bar_index = max(0, total_bars - max_bars_display)

                visible_signals = [
                    signal for signal in signals
                    if (signal.get('bar_index', 0) if not CPP_AVAILABLE else signal.bar_index) >= start_bar_index
                ]

                print(f"📊 Base TF Signal Display Filter: Showing {len(visible_signals)} of {len(signals)} signals (bars {start_bar_index}-{total_bars-1})")
        else:
            visible_signals = signals
            print(f"📊 Signal Display: Showing all {len(signals)} signals")

        for signal in visible_signals:
            if CPP_AVAILABLE:
                signal_type = signal.type
                price = signal.price
                bar_index = signal.bar_index
            else:
                signal_type = signal.get('type', 'BUY')
                price = signal.get('price', 0)
                bar_index = signal.get('bar_index', 0)

            # Choose color and symbol based on signal type
            if signal_type == 'BUY' or (CPP_AVAILABLE and hasattr(fibonacci_calc, 'SignalType') and signal_type == fibonacci_calc.SignalType.BUY):
                color = 'green'
                symbol = 'o'
            elif signal_type == 'SELL' or (CPP_AVAILABLE and hasattr(fibonacci_calc, 'SignalType') and signal_type == fibonacci_calc.SignalType.SELL):
                color = 'red'
                symbol = 'o'
            elif signal_type == 'TAKE_PROFIT' or (CPP_AVAILABLE and hasattr(fibonacci_calc, 'SignalType') and signal_type == fibonacci_calc.SignalType.TAKE_PROFIT):
                color = 'blue'
                symbol = 's'
            else:  # STOP_LOSS
                color = 'orange'
                symbol = 's'

            # Create signal marker
            signal_item = pg.ScatterPlotItem(
                x=[bar_index], y=[price],
                pen=pg.mkPen(color=color, width=2),
                brush=pg.mkBrush(color=color),
                size=8, symbol=symbol
            )
            self.chart.addItem(signal_item)
            self.signal_items.append(signal_item)


class EnhancedTradingChartWidget(QWidget):
    """Enhanced trading chart with Fibonacci overlays"""
    
    def __init__(self):
        super().__init__()
        self.current_data = None
        self.fibonacci_calculator = None
        self.fibonacci_overlay = None
        self.mtf_candle_item = None
        self.setup_ui()
        self.setup_calculator()
    
    def setup_ui(self):
        layout = QVBoxLayout()
        
        # Create splitter for price and volume charts
        splitter = QSplitter(Qt.Orientation.Vertical)
        
        # Price chart with Fibonacci overlays
        self.price_chart = PlotWidget()
        self.price_chart.setLabel('left', 'Price', units='$')
        self.price_chart.setLabel('bottom', 'Time')
        self.price_chart.showGrid(x=False, y=False)  # Grid disabled for cleaner appearance
        self.price_chart.setMinimumHeight(400)

        # Only add the price chart (volume chart removed for cleaner interface)
        splitter.addWidget(self.price_chart)
        
        layout.addWidget(splitter)
        self.setLayout(layout)
        
        # Initialize Fibonacci overlay
        self.fibonacci_overlay = FibonacciOverlay(self.price_chart)
    
    def setup_calculator(self):
        """Initialize the appropriate Fibonacci calculator"""
        if CPP_AVAILABLE:
            self.fibonacci_calculator = fibonacci_calc.FibonacciCalculator()
            print("Using high-performance C++ Fibonacci calculator")
        else:
            self.fibonacci_calculator = PythonFibonacciCalculator()
            print("Using Python Fibonacci calculator")
    
    def update_chart(self, data, fibonacci_settings=None, mtf_candles_settings=None):
        """Update chart with new data and calculate Fibonacci levels"""
        print(f"\n🔄 CHART UPDATE - Data changed, invalidating all cached calculations")
        print(f"   New data: {len(data)} bars from {data.index[0] if not data.empty else 'N/A'} to {data.index[-1] if not data.empty else 'N/A'}")

        # Store previous data info for comparison
        data_changed = True
        if hasattr(self, 'current_data') and self.current_data is not None:
            if len(self.current_data) == len(data) and not data.empty and not self.current_data.empty:
                # Check if it's the same data (same start and end times)
                if (data.index[0] == self.current_data.index[0] and
                    data.index[-1] == self.current_data.index[-1]):
                    data_changed = False
                    print(f"   📊 Data appears unchanged - skipping invalidation")

        if data_changed:
            print(f"   🗑️ Data changed - clearing all cached MTF calculations")
            # Invalidate any cached MTF calculations in the Fibonacci calculator
            if hasattr(self, 'fibonacci_calculator') and self.fibonacci_calculator:
                if hasattr(self.fibonacci_calculator, 'data'):
                    self.fibonacci_calculator.data = []
                if hasattr(self.fibonacci_calculator, 'original_data'):
                    self.fibonacci_calculator.original_data = None

        self.current_data = data

        # Clear existing plots
        self.price_chart.clear()

        if data.empty:
            print(f"   ❌ Empty data - nothing to display")
            return

        # Data is ready for plotting

        # Create candlestick plot
        candlestick = CandlestickItem(data)
        self.price_chart.addItem(candlestick)

        # Add MTF candles if enabled (using separate settings)
        if mtf_candles_settings and mtf_candles_settings.get('show_mtf_candles', False):
            self.add_mtf_candles(data, mtf_candles_settings)
        else:
            # Clear any existing MTF candles if disabled
            if hasattr(self, 'mtf_candle_item') and self.mtf_candle_item:
                self.price_chart.removeItem(self.mtf_candle_item)
                self.mtf_candle_item = None

        # Volume chart removed for cleaner interface

        # Calculate and display Fibonacci levels ONLY if enabled
        if fibonacci_settings and fibonacci_settings.get('show_fibonacci', False):
            self.calculate_and_display_fibonacci(data, fibonacci_settings, mtf_candles_settings)
        else:
            # Clear any existing Fibonacci overlays if disabled
            if hasattr(self, 'fibonacci_overlay') and self.fibonacci_overlay:
                self.fibonacci_overlay.clear_fibonacci()

        # Set x-axis labels
        self.setup_axis_labels(data)
    
    def calculate_and_display_fibonacci(self, data, settings, mtf_candles_settings=None):
        """Calculate Fibonacci levels using C++ calculator and display them"""
        try:
            print(f"\n🧮 FIBONACCI CALCULATION START")
            print(f"   Settings: {settings}")
            print(f"   Data: {len(data)} bars")

            # Check if Fibonacci should be displayed
            if not settings.get('show_fibonacci', False):
                print("   ❌ Fibonacci display disabled - clearing existing levels")
                if hasattr(self, 'fibonacci_overlay') and self.fibonacci_overlay:
                    self.fibonacci_overlay.clear_fibonacci()
                return

            # Validate data
            if data.empty:
                print("   ❌ Empty data - cannot calculate Fibonacci")
                return

            # Detect current timeframe from data
            current_tf_minutes = self.detect_timeframe(data)
            print(f"Detected current timeframe: {current_tf_minutes} minutes")

            # Configure calculator
            if hasattr(self.fibonacci_calculator, 'set_mtf_mode'):
                mtf_enabled = settings.get('use_mtf_mode', False)
                mtf_period = settings.get('mtf_period', 5)

                # Only enable MTF if MTF period is larger than current timeframe
                if mtf_enabled and mtf_period <= current_tf_minutes:
                    print(f"Warning: MTF period ({mtf_period}m) should be larger than current timeframe ({current_tf_minutes}m)")
                    print("Disabling MTF mode")
                    mtf_enabled = False

                self.fibonacci_calculator.set_mtf_mode(
                    mtf_enabled,
                    mtf_period,
                    current_tf_minutes
                )

            if hasattr(self.fibonacci_calculator, 'set_max_bars_back'):
                self.fibonacci_calculator.set_max_bars_back(settings.get('max_bars_back', 50))

            # Convert data for calculator
            if CPP_AVAILABLE:
                # Convert to C++ format
                ohlc_bars = []
                for idx, row in data.iterrows():
                    timestamp = int(idx.timestamp() * 1000) if hasattr(idx, 'timestamp') else int(idx)
                    bar = fibonacci_calc.OHLCBar(
                        row['Open'], row['High'], row['Low'],
                        row['Close'], row['Volume'], timestamp
                    )
                    ohlc_bars.append(bar)

                self.fibonacci_calculator.set_data(ohlc_bars)
            else:
                # Use Python format
                self.fibonacci_calculator.set_data(data)

            # Calculate Fibonacci levels
            fibonacci_results = self.fibonacci_calculator.calculate_fibonacci()

            # Generate trading signals
            signals = self.fibonacci_calculator.generate_signals()

            # Display on chart
            if fibonacci_results:
                max_display = settings.get('max_bars_display', 50)

                # DETAILED LOGGING FOR DEBUGGING
                print(f"\n🔍 FIBONACCI POSITIONING DEBUG:")
                print(f"   Fibonacci results count: {len(fibonacci_results)}")
                print(f"   Fibonacci calculator MTF mode: {hasattr(self.fibonacci_calculator, 'mtf_mode') and self.fibonacci_calculator.mtf_mode}")
                if hasattr(self.fibonacci_calculator, 'mtf_mode'):
                    print(f"   Fibonacci calculator MTF period: {getattr(self.fibonacci_calculator, 'mtf_period', 'N/A')}")

                # Determine MTF period for positioning - INDEPENDENT from MTF candles
                mtf_period_minutes = None
                if hasattr(self.fibonacci_calculator, 'mtf_mode') and self.fibonacci_calculator.mtf_mode:
                    # MTF Fibonacci is enabled - use its own period (independent from MTF candles)
                    mtf_period_minutes = self.fibonacci_calculator.mtf_period
                    print(f"   ✅ Using Fibonacci's own MTF period for positioning: {mtf_period_minutes}m")
                else:
                    print(f"   ❌ MTF Fibonacci disabled - using regular positioning")

                print(f"   Final MTF period for Fibonacci positioning: {mtf_period_minutes}m")
                print(f"   Current timeframe: {current_tf_minutes}m")

                # PRICE & TIMESTAMP ALIGNMENT VERIFICATION
                print(f"\n🎯 PRICE & TIMESTAMP ALIGNMENT VERIFICATION:")
                if fibonacci_results and len(fibonacci_results) > 0:
                    for i in range(min(3, len(fibonacci_results))):
                        result = fibonacci_results[i]
                        if CPP_AVAILABLE:
                            green_level = result.green_level
                            red_level = result.red_level
                            range_high = result.range_high
                            range_low = result.range_low
                            timestamp = result.timestamp
                        else:
                            green_level = result.get('green_level', 0)
                            red_level = result.get('red_level', 0)
                            range_high = result.get('range_high', 0)
                            range_low = result.get('range_low', 0)
                            timestamp = result.get('timestamp', 0)

                        # Get the datetime from the working data
                        working_data = self.fibonacci_calculator.aggregate_to_mtf() if hasattr(self.fibonacci_calculator, 'mtf_mode') and self.fibonacci_calculator.mtf_mode else []
                        fib_datetime = working_data[i]['datetime'] if i < len(working_data) else 'N/A'

                        print(f"   Fib {i}: {fib_datetime} | Green={green_level:.2f}, Red={red_level:.2f}, Range={range_high:.2f}-{range_low:.2f}")
                        print(f"           Green should = Range High: {'✅' if abs(green_level - range_high) < 0.01 else '❌'}")
                        print(f"           Red should = Range Low: {'✅' if abs(red_level - range_low) < 0.01 else '❌'}")

                self.fibonacci_overlay.draw_fibonacci_levels(
                    fibonacci_results,
                    max_display,
                    mtf_period_minutes,
                    current_tf_minutes,
                    data  # Pass base data for timestamp alignment
                )
                print(f"   Displayed {len(fibonacci_results)} Fibonacci results")

            if signals and settings.get('show_signals', True):
                # Pass max_bars_display and MTF info to respect display limits
                total_bars = len(data) if data is not None else 0
                mtf_mode = settings.get('use_mtf_mode', False)
                mtf_results_count = len(fibonacci_results) if fibonacci_results else 0
                mtf_period = settings.get('mtf_period', 5)

                self.fibonacci_overlay.draw_signals(
                    signals,
                    max_display,
                    total_bars,
                    mtf_mode=mtf_mode,
                    mtf_results_count=mtf_results_count,
                    mtf_period=mtf_period
                )
                print(f"Displayed {len(signals)} trading signals")

        except Exception as e:
            print(f"Error calculating Fibonacci levels: {e}")
            import traceback
            traceback.print_exc()

    def detect_timeframe(self, data):
        """Detect the timeframe of the data in minutes"""
        if len(data) < 2:
            return 1  # Default to 1 minute

        # Calculate time difference between first two bars
        time_diff = data.index[1] - data.index[0]

        # Convert to minutes
        if hasattr(time_diff, 'total_seconds'):
            minutes = time_diff.total_seconds() / 60
        else:
            # Handle different pandas timestamp formats
            minutes = time_diff.value / (1000000000 * 60)  # nanoseconds to minutes

        # Round to nearest common timeframe
        common_timeframes = [1, 5, 15, 30, 60, 240, 1440]
        minutes = round(minutes)

        # Find closest common timeframe
        closest_tf = min(common_timeframes, key=lambda x: abs(x - minutes))

        print(f"Time difference: {time_diff}, Detected: {minutes}min, Using: {closest_tf}min")
        return closest_tf

    def add_mtf_candles(self, data, settings):
        """Add multi-timeframe candles overlay - independent from Fibonacci"""
        try:
            # Use MTF candles specific settings (independent from Fibonacci)
            mtf_period = settings.get('mtf_candles_period', 15)
            current_tf = self.detect_timeframe(data)

            print(f"\n🕯️ MTF CANDLES DEBUG:")
            print(f"   MTF period: {mtf_period}m")
            print(f"   Current timeframe: {current_tf}m")
            print(f"   Data length: {len(data)} bars")

            # Only show MTF candles if MTF period is larger than current timeframe
            if mtf_period <= current_tf:
                print(f"   ❌ MTF candles disabled: MTF period ({mtf_period}m) should be larger than current TF ({current_tf}m)")
                return

            # Create MTF data using consistent aggregation logic
            calc = PythonFibonacciCalculator()
            calc.set_mtf_mode(True, mtf_period, current_tf)
            calc.set_data(data)

            # Get MTF aggregated data
            mtf_data_list = calc.aggregate_to_mtf()

            if not mtf_data_list:
                print("   ❌ No MTF data generated")
                return

            # Convert MTF data back to DataFrame for plotting
            # IMPORTANT: Use the same timestamps as Fibonacci for perfect alignment
            mtf_records = []
            mtf_timestamps = []
            for mtf_bar in mtf_data_list:
                mtf_records.append({
                    'Open': mtf_bar['open'],
                    'High': mtf_bar['high'],
                    'Low': mtf_bar['low'],
                    'Close': mtf_bar['close'],
                    'Volume': mtf_bar['volume']
                })
                # Use the same datetime as Fibonacci calculation
                mtf_timestamps.append(mtf_bar['datetime'])

            if not mtf_records:
                print("   ❌ No MTF records to display")
                return

            # Create DataFrame with SAME timestamps as Fibonacci (this fixes alignment!)
            mtf_df = pd.DataFrame(mtf_records, index=mtf_timestamps)

            print(f"   ✅ MTF DataFrame: {mtf_df.shape} candles")

            # Create MTF candle overlay using timestamp-aligned positioning
            transparency = settings.get('transparency', 60)
            self.mtf_candle_item = MTFCandleItem(mtf_df, data, mtf_period, current_tf, transparency)
            self.price_chart.addItem(self.mtf_candle_item)

            print(f"   ✅ Added {len(mtf_df)} MTF candles ({mtf_period}m) over {len(data)} base candles")

            # MTF CANDLES PRICE & TIMESTAMP VERIFICATION
            print(f"\n🕯️ MTF CANDLES PRICE & TIMESTAMP VERIFICATION:")
            for i in range(min(3, len(mtf_df))):
                row = mtf_df.iloc[i]
                timestamp = mtf_df.index[i]
                print(f"   Candle {i}: {timestamp} | O={row['Open']:.2f}, H={row['High']:.2f}, L={row['Low']:.2f}, C={row['Close']:.2f}")

            # Add legend text
            legend_text = f"MTF {settings.get('mtf_candles_period_text', f'{mtf_period}m')} Candles"
            legend_item = pg.TextItem(
                text=legend_text,
                color='cyan',
                anchor=(1, 0)
            )
            # Position legend in top-right corner
            view_box = self.price_chart.getViewBox()
            view_range = view_box.viewRange()
            legend_item.setPos(view_range[0][1] * 0.95, view_range[1][1] * 0.95)
            self.price_chart.addItem(legend_item)

        except Exception as e:
            print(f"Error adding MTF candles: {e}")
            import traceback
            traceback.print_exc()
    
    def setup_axis_labels(self, data):
        """Setup x-axis labels for time"""
        x_labels = []
        for i in range(0, len(data), max(1, len(data)//10)):
            timestamp = data.index[i]
            if hasattr(timestamp, 'strftime'):
                x_labels.append((i, timestamp.strftime('%m/%d')))
        
        self.price_chart.getAxis('bottom').setTicks([x_labels])


class FibonacciControlPanel(QGroupBox):
    """Control panel for Fibonacci settings"""

    def __init__(self):
        super().__init__("Fibonacci Settings")
        self.setup_ui()

    def setup_ui(self):
        layout = QGridLayout()

        # Fibonacci display options
        self.show_fibonacci = QCheckBox("Show Fibonacci Levels")
        self.show_fibonacci.setChecked(True)
        layout.addWidget(self.show_fibonacci, 0, 0, 1, 2)

        self.show_signals = QCheckBox("Show Trading Signals")
        self.show_signals.setChecked(True)
        layout.addWidget(self.show_signals, 1, 0, 1, 2)

        # MTF Mode for Fibonacci only
        self.use_mtf_mode = QCheckBox("Multi-Timeframe Fibonacci")
        layout.addWidget(self.use_mtf_mode, 2, 0, 1, 2)

        # MTF Period for Fibonacci
        layout.addWidget(QLabel("Fib MTF Period:"), 3, 0)
        self.mtf_period = QComboBox()
        self.mtf_period.addItems(["1m", "5m", "15m", "30m", "1h", "4h", "1d"])
        self.mtf_period.setCurrentText("5m")
        layout.addWidget(self.mtf_period, 3, 1)

        # Max bars to display
        layout.addWidget(QLabel("Max Bars Display:"), 4, 0)
        self.max_bars_display = QSpinBox()
        self.max_bars_display.setRange(10, 10000)  # Increased max limit to 10,000
        self.max_bars_display.setValue(50)  # Keep default at 50
        self.max_bars_display.setSingleStep(10)  # Step by 10 for easier navigation
        self.max_bars_display.setToolTip("Maximum number of Fibonacci bars to display (10-10,000)")
        layout.addWidget(self.max_bars_display, 4, 1)

        # Max bars back for calculation
        layout.addWidget(QLabel("Max Bars Back:"), 5, 0)
        self.max_bars_back = QSpinBox()
        self.max_bars_back.setRange(50, 10000)  # Range from 50 to 10,000
        self.max_bars_back.setValue(100)  # Keep default at 100
        self.max_bars_back.setSingleStep(50)  # Step by 50 for easier navigation
        self.max_bars_back.setToolTip("Maximum number of bars to look back for calculations (50-10,000)")
        layout.addWidget(self.max_bars_back, 5, 1)

        # Performance indicator
        if CPP_AVAILABLE:
            perf_label = QLabel("🚀 C++ Acceleration: ON")
            perf_label.setStyleSheet("color: green; font-weight: bold;")
        else:
            perf_label = QLabel("⚠️ Python Fallback: ON")
            perf_label.setStyleSheet("color: orange; font-weight: bold;")

        layout.addWidget(perf_label, 6, 0, 1, 2)

        # Status indicator
        self.status_label = QLabel("📊 Ready - Load data to begin")
        self.status_label.setStyleSheet("color: #888888; font-style: italic;")
        layout.addWidget(self.status_label, 7, 0, 1, 2)

        # Performance note
        perf_note = QLabel("💡 Higher values may impact performance")
        perf_note.setStyleSheet("color: #666666; font-size: 10px; font-style: italic;")
        layout.addWidget(perf_note, 8, 0, 1, 2)

        self.setLayout(layout)
    
    def get_settings(self):
        """Get current Fibonacci settings"""
        # Convert MTF period string to minutes
        mtf_text = self.mtf_period.currentText()
        mtf_minutes = self.parse_timeframe_to_minutes(mtf_text)

        settings = {
            'show_fibonacci': self.show_fibonacci.isChecked(),
            'show_signals': self.show_signals.isChecked(),
            'use_mtf_mode': self.use_mtf_mode.isChecked(),
            'mtf_period': mtf_minutes,
            'mtf_period_text': mtf_text,
            'current_tf_period': 1,  # Assuming 1-minute base timeframe
            'max_bars_display': self.max_bars_display.value(),
            'max_bars_back': self.max_bars_back.value()
        }

        print(f"Fibonacci settings retrieved: {settings}")
        return settings

    def parse_timeframe_to_minutes(self, timeframe_str):
        """Convert timeframe string to minutes"""
        timeframe_map = {
            '1m': 1,
            '5m': 5,
            '15m': 15,
            '30m': 30,
            '1h': 60,
            '4h': 240,
            '1d': 1440
        }
        return timeframe_map.get(timeframe_str, 5)


class MTFCandlesControlPanel(QGroupBox):
    """Separate control panel for MTF Candles settings"""

    def __init__(self):
        super().__init__("MTF Candles Settings")
        self.setup_ui()

    def setup_ui(self):
        layout = QGridLayout()

        # MTF Candles display options
        self.show_mtf_candles = QCheckBox("Show MTF Candles")
        self.show_mtf_candles.setChecked(False)
        layout.addWidget(self.show_mtf_candles, 0, 0, 1, 2)

        # MTF Candles Period (independent from Fibonacci)
        layout.addWidget(QLabel("MTF Candles Period:"), 1, 0)
        self.mtf_candles_period = QComboBox()
        self.mtf_candles_period.addItems(["5m", "15m", "30m", "1h", "4h", "1d"])
        self.mtf_candles_period.setCurrentText("15m")
        layout.addWidget(self.mtf_candles_period, 1, 1)

        # MTF Candles Style options
        layout.addWidget(QLabel("Candle Style:"), 2, 0)
        self.candle_style = QComboBox()
        self.candle_style.addItems(["TradingView", "Outlined", "Solid"])
        self.candle_style.setCurrentText("TradingView")
        layout.addWidget(self.candle_style, 2, 1)

        # Transparency
        layout.addWidget(QLabel("Transparency:"), 3, 0)
        self.transparency = QSlider(Qt.Orientation.Horizontal)
        self.transparency.setRange(20, 100)
        self.transparency.setValue(60)
        self.transparency.setTickPosition(QSlider.TickPosition.TicksBelow)
        self.transparency.setTickInterval(20)
        layout.addWidget(self.transparency, 3, 1)

        # Status indicator
        self.mtf_status_label = QLabel("🕯️ MTF Candles: OFF")
        self.mtf_status_label.setStyleSheet("color: #888888; font-style: italic;")
        layout.addWidget(self.mtf_status_label, 4, 0, 1, 2)

        # Connect signals to update status
        self.show_mtf_candles.toggled.connect(self.update_status)
        self.mtf_candles_period.currentTextChanged.connect(self.update_status)

        self.setLayout(layout)
        self.update_status()

    def update_status(self):
        """Update the status label"""
        if self.show_mtf_candles.isChecked():
            period = self.mtf_candles_period.currentText()
            self.mtf_status_label.setText(f"🕯️ MTF Candles: {period} - ON")
            self.mtf_status_label.setStyleSheet("color: #00FFFF; font-weight: bold;")
        else:
            self.mtf_status_label.setText("🕯️ MTF Candles: OFF")
            self.mtf_status_label.setStyleSheet("color: #888888; font-style: italic;")

    def get_settings(self):
        """Get current MTF Candles settings"""
        mtf_text = self.mtf_candles_period.currentText()
        mtf_minutes = self.parse_timeframe_to_minutes(mtf_text)

        settings = {
            'show_mtf_candles': self.show_mtf_candles.isChecked(),
            'mtf_candles_period': mtf_minutes,
            'mtf_candles_period_text': mtf_text,
            'candle_style': self.candle_style.currentText(),
            'transparency': self.transparency.value()
        }

        print(f"MTF Candles settings retrieved: {settings}")
        return settings

    def parse_timeframe_to_minutes(self, timeframe_str):
        """Convert timeframe string to minutes"""
        timeframe_map = {
            '1m': 1,
            '5m': 5,
            '15m': 15,
            '30m': 30,
            '1h': 60,
            '4h': 240,
            '1d': 1440
        }
        return timeframe_map.get(timeframe_str, 15)


class EnhancedMainWindow(QMainWindow):
    """Enhanced main window with Fibonacci integration"""

    def __init__(self):
        super().__init__()
        self.data_fetcher = None
        self.setup_ui()
        self.setup_style()

        # Auto-load default data after UI is set up
        QTimer.singleShot(1000, self.auto_load_default_data)  # Load after 1 second delay

    def setup_ui(self):
        self.setWindowTitle("Enhanced Trading Chart - Fibonacci GUI with C++ Acceleration")
        self.setGeometry(100, 100, 1400, 900)

        # Central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # Main layout
        main_layout = QHBoxLayout()

        # Left panel with controls
        left_panel = QVBoxLayout()

        # Create integrated control panel
        self.control_panel = self.create_trading_control_panel()
        self.control_panel.setMaximumWidth(280)

        # Fibonacci controls
        self.fibonacci_panel = FibonacciControlPanel()
        self.fibonacci_panel.setMaximumWidth(280)

        # MTF Candles controls (separate panel)
        self.mtf_candles_panel = MTFCandlesControlPanel()
        self.mtf_candles_panel.setMaximumWidth(280)

        # Update buttons
        self.update_fibonacci_btn = QPushButton("🔄 Update Fibonacci")
        self.update_fibonacci_btn.clicked.connect(self.update_fibonacci_display)

        self.update_mtf_candles_btn = QPushButton("🕯️ Update MTF Candles")
        self.update_mtf_candles_btn.clicked.connect(self.update_mtf_candles_display)
        # Style the buttons
        button_style = """
            QPushButton {
                background-color: #0078d4;
                color: white;
                font-weight: bold;
                padding: 8px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #106ebe;
            }
            QPushButton:pressed {
                background-color: #005a9e;
            }
        """
        self.update_fibonacci_btn.setStyleSheet(button_style)

        self.update_mtf_candles_btn.setStyleSheet("""
            QPushButton {
                background-color: #FF6B35;
                color: white;
                font-weight: bold;
                padding: 8px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #E55A2B;
            }
            QPushButton:pressed {
                background-color: #CC4A21;
            }
        """)

        left_panel.addWidget(self.control_panel)
        left_panel.addWidget(self.fibonacci_panel)
        left_panel.addWidget(self.update_fibonacci_btn)
        left_panel.addWidget(self.mtf_candles_panel)
        left_panel.addWidget(self.update_mtf_candles_btn)
        left_panel.addStretch()

        # Chart widget
        self.chart_widget = EnhancedTradingChartWidget()

        # Add to main layout
        left_widget = QWidget()
        left_widget.setLayout(left_panel)
        main_layout.addWidget(left_widget, 1)
        main_layout.addWidget(self.chart_widget, 4)

        central_widget.setLayout(main_layout)

        # Status bar
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)

        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.status_bar.addPermanentWidget(self.progress_bar)

        # Controls are now directly accessible as instance variables
        # No need for findChild since we created them directly

    def create_trading_control_panel(self):
        """Create the integrated trading control panel"""
        panel = QGroupBox("Trading Controls")
        layout = QGridLayout()

        # Symbol input
        layout.addWidget(QLabel("Symbol:"), 0, 0)
        self.symbol_input = QLineEdit("AAPL")
        self.symbol_input.setPlaceholderText("Enter ticker symbol")
        layout.addWidget(self.symbol_input, 0, 1)

        # Period selection
        layout.addWidget(QLabel("Period:"), 1, 0)
        self.period_combo = QComboBox()
        self.period_combo.addItems(["1d", "5d", "1mo", "3mo", "6mo", "1y", "2y", "5y", "10y", "ytd", "max"])
        self.period_combo.setCurrentText("1mo")
        layout.addWidget(self.period_combo, 1, 1)

        # Interval selection
        layout.addWidget(QLabel("Interval:"), 2, 0)
        self.interval_combo = QComboBox()
        self.interval_combo.addItems(["1m", "2m", "5m", "15m", "30m", "60m", "90m", "1h", "1d", "5d", "1wk", "1mo", "3mo"])
        self.interval_combo.setCurrentText("1d")
        layout.addWidget(self.interval_combo, 2, 1)

        # Fetch button
        self.fetch_button = QPushButton("Fetch Data")
        self.fetch_button.clicked.connect(self.fetch_data)
        layout.addWidget(self.fetch_button, 3, 0, 1, 2)

        # CSV Upload button
        self.csv_upload_button = QPushButton("📁 Upload CSV")
        self.csv_upload_button.clicked.connect(self.upload_csv_data)
        self.csv_upload_button.setToolTip(
            "Upload CSV file with Multi-Timeframe OHLCV data\n\n"
            "Required columns:\n"
            "• Date/Datetime/Time/Timestamp\n"
            "• Open, High, Low, Close, Volume\n\n"
            "MUST contain ALL THREE timeframes:\n"
            "• Second-level data (1-59 seconds)\n"
            "• Minute-level data (1-59 minutes)\n"
            "• Hour-level data (1-23 hours)\n\n"
            "Date format: ISO (YYYY-MM-DD HH:MM:SS)\n"
            "or any standard date format"
        )
        self.csv_upload_button.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                font-weight: bold;
                padding: 6px;
                border-radius: 3px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
            QPushButton:pressed {
                background-color: #1e7e34;
            }
        """)
        layout.addWidget(self.csv_upload_button, 4, 0, 1, 1)

        # Reset to Yahoo Finance button
        self.reset_to_yahoo_button = QPushButton("🔄 Yahoo")
        self.reset_to_yahoo_button.clicked.connect(self.reset_to_yahoo_finance)
        self.reset_to_yahoo_button.setToolTip("Reset to Yahoo Finance mode\nand clear CSV data")
        self.reset_to_yahoo_button.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                font-weight: bold;
                padding: 6px;
                border-radius: 3px;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
            QPushButton:pressed {
                background-color: #545b62;
            }
        """)
        layout.addWidget(self.reset_to_yahoo_button, 4, 1, 1, 1)

        # Popular symbols
        layout.addWidget(QLabel("Quick Select:"), 5, 0, 1, 2)
        popular_symbols = ["AAPL", "GOOGL", "MSFT", "TSLA", "AMZN", "NVDA", "META", "SPY", "QQQ", "BTC-USD", "ETH-USD"]

        row = 6
        for i, symbol in enumerate(popular_symbols):
            if i % 2 == 0 and i > 0:
                row += 1
            btn = QPushButton(symbol)
            btn.clicked.connect(lambda _, s=symbol: self.set_symbol(s))
            layout.addWidget(btn, row, i % 2)

        panel.setLayout(layout)
        return panel

    def setup_style(self):
        """Setup application styling"""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #2b2b2b;
                color: #ffffff;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #555555;
                border-radius: 5px;
                margin-top: 1ex;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            QPushButton {
                background-color: #404040;
                border: 1px solid #555555;
                padding: 5px;
                border-radius: 3px;
            }
            QPushButton:hover {
                background-color: #505050;
            }
            QPushButton:pressed {
                background-color: #353535;
            }
            QComboBox, QLineEdit, QSpinBox {
                background-color: #404040;
                border: 1px solid #555555;
                padding: 3px;
                border-radius: 3px;
            }
            QCheckBox {
                spacing: 5px;
            }
            QCheckBox::indicator {
                width: 13px;
                height: 13px;
            }
            QCheckBox::indicator:unchecked {
                border: 1px solid #555555;
                background-color: #404040;
            }
            QCheckBox::indicator:checked {
                border: 1px solid #555555;
                background-color: #0078d4;
            }
        """)

    def auto_load_default_data(self):
        """Auto-load default data on startup"""
        print("🔄 Auto-loading default data (AAPL, 1d, 1m)...")
        if self.symbol_input and self.period_combo and self.interval_combo:
            # Set to 1-minute data for better Fibonacci signals
            self.interval_combo.setCurrentText("1m")
            self.period_combo.setCurrentText("1d")  # 1 day of 1-minute data
            self.fetch_data()
        else:
            print("❌ Controls not ready for auto-load")

    def set_symbol(self, symbol):
        """Set symbol from quick select buttons"""
        if self.symbol_input:
            self.symbol_input.setText(symbol)
            self.fetch_data()

    def fetch_data(self):
        """Fetch data and update chart with Fibonacci levels"""
        if not self.symbol_input or not self.period_combo or not self.interval_combo:
            QMessageBox.warning(self, "Warning", "Control panel not properly initialized")
            return

        symbol = self.symbol_input.text().strip().upper()
        period = self.period_combo.currentText()
        interval = self.interval_combo.currentText()

        if not symbol:
            QMessageBox.warning(self, "Warning", "Please enter a symbol")
            return

        # Disable fetch button and show progress
        if self.fetch_button:
            self.fetch_button.setEnabled(False)
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        self.status_bar.showMessage(f"Fetching data for {symbol}...")

        # Start data fetcher thread
        self.data_fetcher = DataFetcher(symbol, period, interval)
        self.data_fetcher.data_ready.connect(self.on_data_ready)
        self.data_fetcher.error_occurred.connect(self.on_error)
        self.data_fetcher.progress_updated.connect(self.progress_bar.setValue)
        self.data_fetcher.start()

    def on_data_ready(self, data):
        """Handle successful data fetch"""
        # Get Fibonacci settings if enabled
        fibonacci_settings = None
        if self.fibonacci_panel.show_fibonacci.isChecked():
            fibonacci_settings = self.fibonacci_panel.get_settings()
            print(f"Data loaded - Fibonacci enabled with settings: {fibonacci_settings}")
        else:
            print("Data loaded - Fibonacci disabled")

        # Get MTF Candles settings if enabled
        mtf_candles_settings = None
        if self.mtf_candles_panel.show_mtf_candles.isChecked():
            mtf_candles_settings = self.mtf_candles_panel.get_settings()
            print(f"Data loaded - MTF Candles enabled with settings: {mtf_candles_settings}")
        else:
            print("Data loaded - MTF Candles disabled")

        self.chart_widget.update_chart(data, fibonacci_settings, mtf_candles_settings)

        if self.fetch_button:
            self.fetch_button.setEnabled(True)
        self.progress_bar.setVisible(False)

        # Show performance info
        calc_type = "C++" if CPP_AVAILABLE else "Python"
        fib_status = "ON" if fibonacci_settings else "OFF"
        mtf_status = "ON" if mtf_candles_settings else "OFF"
        self.status_bar.showMessage(
            f"Data loaded successfully - {len(data)} bars | Fibonacci: {fib_status} | MTF Candles: {mtf_status} | Engine: {calc_type}", 5000
        )

    def on_error(self, error_message):
        """Handle data fetch error"""
        QMessageBox.critical(self, "Error", error_message)
        if self.fetch_button:
            self.fetch_button.setEnabled(True)
        self.progress_bar.setVisible(False)
        self.status_bar.showMessage("Error loading data", 3000)

    def upload_csv_data(self):
        """Handle CSV file upload and display on chart"""
        try:
            # Open file dialog
            file_path, _ = QFileDialog.getOpenFileName(
                self,
                "Upload CSV Data",
                "",
                "CSV Files (*.csv);;All Files (*)"
            )

            if not file_path:
                return  # User cancelled

            # Show progress
            self.csv_upload_button.setEnabled(False)
            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(25)
            self.status_bar.showMessage(f"Loading CSV file: {file_path}")

            # Load and validate CSV data
            try:
                data = CSVDataLoader.load_csv_file(file_path)
                self.progress_bar.setValue(75)

                if data.empty:
                    raise ValueError("No valid data found in CSV file")

                # Update the chart with CSV data
                self.on_csv_data_ready(data, file_path)

            except Exception as e:
                self.progress_bar.setVisible(False)
                self.csv_upload_button.setEnabled(True)
                QMessageBox.critical(
                    self,
                    "CSV Load Error",
                    f"Failed to load CSV file:\n\n{str(e)}\n\n"
                    f"Expected CSV format:\n"
                    f"• Columns: Date/Datetime, Open, High, Low, Close, Volume\n"
                    f"• Date column should be parseable (ISO format recommended)\n"
                    f"• OHLCV columns should contain numeric values\n"
                    f"• High >= Low, High >= Open/Close, Low <= Open/Close\n"
                    f"• MUST contain ALL THREE timeframes:\n"
                    f"  - Second-level data (1-59 seconds)\n"
                    f"  - Minute-level data (1-59 minutes)\n"
                    f"  - Hour-level data (1-23 hours)"
                )
                self.status_bar.showMessage("CSV load failed", 3000)
                return

        except Exception as e:
            self.progress_bar.setVisible(False)
            self.csv_upload_button.setEnabled(True)
            QMessageBox.critical(self, "Error", f"Unexpected error: {str(e)}")
            self.status_bar.showMessage("CSV upload failed", 3000)

    def on_csv_data_ready(self, data, file_path):
        """Handle successful CSV data load"""
        try:
            # Get Fibonacci settings if enabled
            fibonacci_settings = None
            if self.fibonacci_panel.show_fibonacci.isChecked():
                fibonacci_settings = self.fibonacci_panel.get_settings()

            # Get MTF Candles settings if enabled
            mtf_candles_settings = None
            if self.mtf_candles_panel.show_mtf_candles.isChecked():
                mtf_candles_settings = self.mtf_candles_panel.get_settings()

            # Update chart with CSV data and indicators
            self.chart_widget.update_chart(data, fibonacci_settings, mtf_candles_settings)

            # Re-enable controls
            self.csv_upload_button.setEnabled(True)
            self.progress_bar.setVisible(False)

            # Update status with file info
            file_name = file_path.split('/')[-1].split('\\')[-1]  # Get filename from path
            calc_type = "C++" if CPP_AVAILABLE else "Python"
            fib_status = "ON" if fibonacci_settings else "OFF"
            mtf_status = "ON" if mtf_candles_settings else "OFF"

            self.status_bar.showMessage(
                f"CSV loaded: {file_name} - {len(data)} bars | Fibonacci: {fib_status} | MTF Candles: {mtf_status} | Engine: {calc_type}", 5000
            )

            # Clear symbol input to indicate we're using CSV data
            if self.symbol_input:
                self.symbol_input.setText("CSV Data")
                self.symbol_input.setEnabled(False)

        except Exception as e:
            self.csv_upload_button.setEnabled(True)
            self.progress_bar.setVisible(False)
            QMessageBox.critical(self, "Error", f"Failed to display CSV data: {str(e)}")
            self.status_bar.showMessage("CSV display failed", 3000)

    def reset_to_yahoo_finance(self):
        """Reset interface back to Yahoo Finance mode"""
        try:
            # Re-enable symbol input
            if self.symbol_input:
                self.symbol_input.setText("AAPL")
                self.symbol_input.setEnabled(True)

            # Clear the chart
            if hasattr(self, 'chart_widget') and self.chart_widget:
                self.chart_widget.price_chart.clear()
                if hasattr(self.chart_widget, 'fibonacci_overlay') and self.chart_widget.fibonacci_overlay:
                    self.chart_widget.fibonacci_overlay.clear_fibonacci()

            self.status_bar.showMessage("Reset to Yahoo Finance mode - Enter symbol and fetch data", 3000)

        except Exception as e:
            QMessageBox.warning(self, "Warning", f"Error resetting interface: {str(e)}")
            self.status_bar.showMessage("Reset failed", 3000)

    def update_fibonacci_display(self):
        """Update Fibonacci display with current settings"""
        if self.chart_widget.current_data is not None:
            # Check if Fibonacci is enabled
            if self.fibonacci_panel.show_fibonacci.isChecked():
                fibonacci_settings = self.fibonacci_panel.get_settings()

                # Update Fibonacci independently (no MTF candles dependency)
                print(f"Update Fibonacci clicked! Settings: {fibonacci_settings}")
                self.chart_widget.calculate_and_display_fibonacci(
                    self.chart_widget.current_data, fibonacci_settings, None
                )
                self.status_bar.showMessage("Fibonacci levels updated", 2000)
            else:
                # Clear Fibonacci if disabled
                print("Fibonacci disabled - clearing display")
                if hasattr(self.chart_widget, 'fibonacci_overlay') and self.chart_widget.fibonacci_overlay:
                    self.chart_widget.fibonacci_overlay.clear_fibonacci()
                self.status_bar.showMessage("Fibonacci levels cleared", 2000)
        else:
            print("No data available - fetch data first!")
            self.status_bar.showMessage("No data available - fetch data first!", 3000)

    def update_mtf_candles_display(self):
        """Update MTF Candles display with current settings - independent from Fibonacci"""
        if self.chart_widget.current_data is not None:
            # Check if MTF Candles are enabled
            if self.mtf_candles_panel.show_mtf_candles.isChecked():
                mtf_candles_settings = self.mtf_candles_panel.get_settings()
                print(f"Update MTF Candles clicked! Settings: {mtf_candles_settings}")

                # Clear existing MTF candles
                if hasattr(self.chart_widget, 'mtf_candle_item') and self.chart_widget.mtf_candle_item:
                    self.chart_widget.price_chart.removeItem(self.chart_widget.mtf_candle_item)
                    self.chart_widget.mtf_candle_item = None

                # Add new MTF candles (independent from Fibonacci)
                self.chart_widget.add_mtf_candles(self.chart_widget.current_data, mtf_candles_settings)
                self.status_bar.showMessage("MTF Candles updated", 2000)
            else:
                # Clear MTF candles if disabled
                print("MTF Candles disabled - clearing display")
                if hasattr(self.chart_widget, 'mtf_candle_item') and self.chart_widget.mtf_candle_item:
                    self.chart_widget.price_chart.removeItem(self.chart_widget.mtf_candle_item)
                    self.chart_widget.mtf_candle_item = None
                self.status_bar.showMessage("MTF Candles cleared", 2000)
        else:
            print("No data available - fetch data first!")
            self.status_bar.showMessage("No data available - fetch data first!", 3000)


def main():
    app = QApplication(sys.argv)
    app.setApplicationName("Enhanced Trading Chart GUI with Fibonacci")

    # Set dark theme
    app.setStyle('Fusion')
    palette = QPalette()
    palette.setColor(QPalette.ColorRole.Window, QColor(53, 53, 53))
    palette.setColor(QPalette.ColorRole.WindowText, Qt.GlobalColor.white)
    app.setPalette(palette)

    # Show startup message
    if CPP_AVAILABLE:
        print("🚀 Starting Enhanced Trading Chart with C++ Fibonacci Acceleration")
    else:
        print("⚠️ Starting Trading Chart with Python Fibonacci Fallback")

    window = EnhancedMainWindow()
    window.show()

    sys.exit(app.exec())


if __name__ == "__main__":
    main()
