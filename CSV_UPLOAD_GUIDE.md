# CSV Upload Feature Guide

## Overview
The Enhanced Trading Chart now supports uploading CSV files containing OHLCV (Open, High, Low, Close, Volume) data. This allows you to analyze your own trading data with all the advanced indicators and Fibonacci levels.

## CSV Format Requirements

### Required Columns
Your CSV file must contain the following columns (case-insensitive):

1. **Date/Time Column** (one of):
   - `Date`
   - `Datetime` 
   - `Time`
   - `Timestamp`

2. **OHLCV Columns**:
   - `Open` - Opening price
   - `High` - Highest price
   - `Low` - Lowest price  
   - `Close` - Closing price
   - `Volume` - Trading volume (can also be `Vol` or `V`)

### Date Format
The date/time column should be in a standard parseable format:
- **Recommended**: ISO format `YYYY-MM-DD HH:MM:SS` (e.g., `2024-01-01 09:30:00`)
- **Also supported**: US format `MM/DD/YYYY HH:MM:SS`
- **Also supported**: European format `DD/MM/YYYY HH:MM:SS`
- **Also supported**: Unix timestamps

### Data Validation
The system automatically validates your data:
- ✅ Removes rows with missing values
- ✅ Validates OHLC relationships (High ≥ Low, High ≥ Open/Close, etc.)
- ✅ Sorts data by timestamp
- ✅ Detects timeframe automatically

## Example CSV Format

```csv
Date,Open,High,Low,Close,Volume
2024-01-01 09:30:00,150.00,152.50,149.75,151.25,1000000
2024-01-01 09:31:00,151.25,153.00,150.50,152.75,1100000
2024-01-01 09:32:00,152.75,154.25,152.00,153.50,1200000
```

## How to Use

### 1. Upload CSV Data
1. Click the **📁 Upload CSV** button in the Trading Controls panel
2. Select your CSV file from the file dialog
3. The system will automatically validate and load your data
4. If successful, the chart will display your data with candlesticks

### 2. Apply Indicators
Once your CSV data is loaded, you can use all the advanced features:

- **Fibonacci Levels**: Enable in the Fibonacci Settings panel
- **MTF Candles**: Enable Multi-Timeframe candles overlay
- **Trading Signals**: Automatic buy/sell signal generation
- **All Indicators**: All existing indicators work with CSV data

### 3. Switch Back to Yahoo Finance
- Click the **🔄 Yahoo** button to return to live market data mode
- This clears the CSV data and re-enables the symbol input

## Features Available with CSV Data

### ✅ Fully Supported
- Candlestick chart display
- Fibonacci level calculations and display
- Multi-timeframe (MTF) candle overlays
- Trading signal generation
- All technical indicators
- Zoom and pan functionality
- High-performance C++ calculations (if available)

### 📊 Automatic Features
- **Timeframe Detection**: Automatically detects your data's timeframe
- **Data Cleaning**: Removes invalid or incomplete rows
- **OHLC Validation**: Ensures price relationships are correct
- **Sorting**: Automatically sorts data by timestamp

## Troubleshooting

### Common Issues

**"No date/time column found"**
- Ensure your CSV has a column named Date, Datetime, Time, or Timestamp
- Check for extra spaces in column names

**"Required column 'open' not found"**
- Verify all OHLCV columns are present
- Column names are case-insensitive but must be spelled correctly

**"Could not parse date column"**
- Use ISO format: `YYYY-MM-DD HH:MM:SS`
- Ensure dates are consistent throughout the file

**"No valid data rows after cleaning"**
- Check for missing values in OHLCV columns
- Verify High ≥ Low and other OHLC relationships
- Ensure numeric values are properly formatted

### Sample Data
A sample CSV file (`sample_data.csv`) is included in the project directory for testing.

## Technical Details

### Performance
- Uses pandas for efficient CSV parsing
- Leverages the same high-performance C++ engine for calculations
- Handles large datasets (tested with 10,000+ rows)

### Memory Usage
- Data is loaded into memory for fast access
- Automatic garbage collection of previous data
- Efficient data structures for real-time calculations

### Compatibility
- Works with all existing indicator settings
- Compatible with both C++ and Python calculation engines
- Supports all timeframes (1-minute to daily data)
