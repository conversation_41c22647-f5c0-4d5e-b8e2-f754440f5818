# CSV Upload Feature Guide

## Overview
The Enhanced Trading Chart now supports uploading CSV files containing Multi-Timeframe OHLCV (Open, High, Low, Close, Volume) data. This allows you to analyze your own trading data with all the advanced indicators and Fibonacci levels across multiple timeframes.

## CSV Format Requirements

### Required Columns
Your CSV file must contain the following columns (case-insensitive):

1. **Date/Time Column** (one of):
   - `Date`
   - `Datetime`
   - `Time`
   - `Timestamp`

2. **OHLCV Columns**:
   - `Open` - Opening price
   - `High` - Highest price
   - `Low` - Lowest price
   - `Close` - Closing price
   - `Volume` - Trading volume (can also be `Vol` or `V`)

### 🚨 CRITICAL: Multi-Timeframe Requirement
**Your CSV MUST contain data at ALL THREE timeframe levels:**

1. **Second-level data** (intervals of 1-59 seconds)
   - Examples: 5s, 10s, 15s, 30s intervals

2. **Minute-level data** (intervals of 1-59 minutes)
   - Examples: 1m, 5m, 15m, 30m intervals

3. **Hour-level data** (intervals of 1-23 hours)
   - Examples: 1h, 2h, 4h, 6h intervals

**The system will reject CSV files that don't contain all three timeframe types.**

### Date Format
The date/time column should be in a standard parseable format:
- **Recommended**: ISO format `YYYY-MM-DD HH:MM:SS` (e.g., `2024-01-01 09:30:00`)
- **Also supported**: US format `MM/DD/YYYY HH:MM:SS`
- **Also supported**: European format `DD/MM/YYYY HH:MM:SS`
- **Also supported**: Unix timestamps

### Data Validation
The system automatically validates your data:
- ✅ Removes rows with missing values
- ✅ Validates OHLC relationships (High ≥ Low, High ≥ Open/Close, etc.)
- ✅ Sorts data by timestamp
- ✅ **Verifies all three timeframes are present**
- ✅ Provides detailed timeframe analysis

## Example Multi-Timeframe CSV Format

```csv
Date,Open,High,Low,Close,Volume
2024-01-01 09:30:00,150.00,152.50,149.75,151.25,1000000
2024-01-01 09:30:15,151.25,151.75,150.50,151.50,250000
2024-01-01 09:30:30,151.50,152.00,151.00,151.75,300000
2024-01-01 09:31:00,152.00,153.00,151.50,152.75,1100000
2024-01-01 09:32:00,152.75,154.25,152.00,153.50,1200000
2024-01-01 09:32:10,153.50,153.80,153.00,153.60,200000
2024-01-01 10:00:00,160.20,161.25,159.70,160.75,1900000
2024-01-01 11:00:00,162.50,164.25,162.00,163.75,2200000
2024-01-01 12:00:00,164.25,166.00,163.75,165.50,2400000
```

**Notice the mixed intervals:**
- `09:30:15` to `09:30:30` = 15 seconds (second-level)
- `09:30:00` to `09:31:00` = 1 minute (minute-level)
- `10:00:00` to `11:00:00` = 1 hour (hour-level)

## How to Use

### 1. Upload CSV Data
1. Click the **📁 Upload CSV** button in the Trading Controls panel
2. Select your CSV file from the file dialog
3. The system will automatically validate and load your data
4. If successful, the chart will display your data with candlesticks

### 2. Apply Indicators
Once your CSV data is loaded, you can use all the advanced features:

- **Fibonacci Levels**: Enable in the Fibonacci Settings panel
- **MTF Candles**: Enable Multi-Timeframe candles overlay
- **Trading Signals**: Automatic buy/sell signal generation
- **All Indicators**: All existing indicators work with CSV data

### 3. Switch Back to Yahoo Finance
- Click the **🔄 Yahoo** button to return to live market data mode
- This clears the CSV data and re-enables the symbol input

## Features Available with CSV Data

### ✅ Fully Supported
- Candlestick chart display
- Fibonacci level calculations and display
- Multi-timeframe (MTF) candle overlays
- Trading signal generation
- All technical indicators
- Zoom and pan functionality
- High-performance C++ calculations (if available)

### 📊 Automatic Features
- **Timeframe Detection**: Automatically detects your data's timeframe
- **Data Cleaning**: Removes invalid or incomplete rows
- **OHLC Validation**: Ensures price relationships are correct
- **Sorting**: Automatically sorts data by timestamp

## Troubleshooting

### Common Issues

**"CSV data is missing required timeframes"**
- **Most Common Error**: Your CSV must contain ALL THREE timeframe levels
- Add data points with second-level intervals (1-59 seconds)
- Add data points with minute-level intervals (1-59 minutes)
- Add data points with hour-level intervals (1-23 hours)
- Example: Mix 15-second, 5-minute, and 1-hour data points

**"No date/time column found"**
- Ensure your CSV has a column named Date, Datetime, Time, or Timestamp
- Check for extra spaces in column names

**"Required column 'open' not found"**
- Verify all OHLCV columns are present
- Column names are case-insensitive but must be spelled correctly

**"Could not parse date column"**
- Use ISO format: `YYYY-MM-DD HH:MM:SS`
- Ensure dates are consistent throughout the file

**"No valid data rows after cleaning"**
- Check for missing values in OHLCV columns
- Verify High ≥ Low and other OHLC relationships
- Ensure numeric values are properly formatted

### Creating Multi-Timeframe Data

If your data only contains one timeframe, you'll need to:

1. **Add Second-level Data**: Include some data points with 1-59 second intervals
2. **Add Minute-level Data**: Include some data points with 1-59 minute intervals
3. **Add Hour-level Data**: Include some data points with 1-23 hour intervals

**Example Strategy:**
- Start with your main timeframe (e.g., 1-minute data)
- Add some 15-second intervals within busy periods
- Add some 1-hour intervals for longer-term view
- Ensure all three types are represented in your dataset

### Sample Data
A sample CSV file (`sample_data.csv`) is included in the project directory for testing.

## Technical Details

### Performance
- Uses pandas for efficient CSV parsing
- Leverages the same high-performance C++ engine for calculations
- Handles large datasets (tested with 10,000+ rows)

### Memory Usage
- Data is loaded into memory for fast access
- Automatic garbage collection of previous data
- Efficient data structures for real-time calculations

### Compatibility
- Works with all existing indicator settings
- Compatible with both C++ and Python calculation engines
- Supports all timeframes (1-minute to daily data)
