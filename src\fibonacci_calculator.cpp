#include "fibonacci_calculator.hpp"
#include <limits>
#include <stdexcept>
#include <pybind11/pybind11.h>
#include <pybind11/stl.h>
#include <pybind11/numpy.h>

namespace trading {

void FibonacciCalculator::setMTFMode(bool enabled, int mtf_period, int current_tf_period) {
    use_mtf_mode_ = enabled;
    mtf_period_ = mtf_period;
    current_tf_period_ = current_tf_period;
}

void FibonacciCalculator::setConfirmedData(bool use_confirmed) {
    use_confirmed_data_ = use_confirmed;
}

void FibonacciCalculator::setMaxBarsBack(int max_bars) {
    max_bars_back_ = max_bars;
}

void FibonacciCalculator::setData(const std::vector<OHLCBar>& data) {
    data_ = data;
    fibonacci_results_.clear();
    signals_.clear();
    
    // Reset signal tracking
    green_signal_triggered_ = false;
    red_signal_triggered_ = false;
    active_buy_position_ = false;
    active_sell_position_ = false;
    buy_tp_level_ = std::numeric_limits<double>::quiet_NaN();
    sell_tp_level_ = std::numeric_limits<double>::quiet_NaN();
    buy_sl_level_ = std::numeric_limits<double>::quiet_NaN();
    sell_sl_level_ = std::numeric_limits<double>::quiet_NaN();
}

void FibonacciCalculator::addBar(const OHLCBar& bar) {
    data_.push_back(bar);
    
    // Maintain max bars limit
    if (data_.size() > static_cast<size_t>(max_bars_back_ * 2)) {
        data_.erase(data_.begin(), data_.begin() + max_bars_back_);
    }
}

std::vector<FibonacciResult> FibonacciCalculator::calculateFibonacci() {
    fibonacci_results_.clear();
    
    if (data_.empty()) {
        return fibonacci_results_;
    }
    
    if (use_mtf_mode_) {
        // MTF Mode: Calculate Fibonacci based on MTF candles
        auto mtf_bars = aggregateToMTF(data_);
        
        for (size_t i = 1; i < mtf_bars.size(); ++i) {
            if (isNewMTFPeriod(mtf_bars[i], mtf_bars[i-1])) {
                // Use previous MTF bar for confirmed data
                const auto& mtf_bar = use_confirmed_data_ ? mtf_bars[i-1] : mtf_bars[i];
                
                long long start_time = mtf_bar.timestamp;
                long long end_time = start_time + timeframeToMilliseconds(mtf_period_);
                
                auto result = calculateFibonacciForRange(mtf_bar.high, mtf_bar.low, start_time, end_time);
                result.is_bullish = mtf_bar.close > mtf_bar.open;
                
                fibonacci_results_.push_back(result);
            }
        }
    } else {
        // Legacy Mode: Calculate Fibonacci for each bar
        for (size_t i = 0; i < data_.size(); ++i) {
            const auto& bar = data_[i];
            auto result = calculateFibonacciForRange(bar.high, bar.low, bar.timestamp, bar.timestamp);
            result.is_bullish = bar.close > bar.open;
            fibonacci_results_.push_back(result);
        }
    }
    
    return fibonacci_results_;
}

FibonacciResult FibonacciCalculator::calculateFibonacciForRange(double high, double low, 
                                                              long long start_time, long long end_time) {
    FibonacciResult result;
    result.range_high = high;
    result.range_low = low;
    result.price_range = high - low;
    result.start_time = start_time;
    result.end_time = end_time;
    
    if (result.price_range <= 0) {
        return result;
    }
    
    // Calculate all Fibonacci levels
    result.levels = getLevelsForRange(low, result.price_range);
    
    // Set key levels for signals
    result.green_level = calculatePriceAtLevel(low, result.price_range, 1.0);  // 100% level
    result.red_level = calculatePriceAtLevel(low, result.price_range, 0.0);    // 0% level
    result.level_0_9 = calculatePriceAtLevel(low, result.price_range, 0.9);    // 90% level for long SL
    result.level_0_1 = calculatePriceAtLevel(low, result.price_range, 0.1);    // 10% level for short SL
    
    return result;
}

std::vector<OHLCBar> FibonacciCalculator::aggregateToMTF(const std::vector<OHLCBar>& bars) {
    std::vector<OHLCBar> mtf_bars;
    
    if (bars.empty()) {
        return mtf_bars;
    }
    
    long long mtf_duration_ms = timeframeToMilliseconds(mtf_period_);
    long long current_mtf_start = (bars[0].timestamp / mtf_duration_ms) * mtf_duration_ms;
    
    double mtf_open = bars[0].open;
    double mtf_high = bars[0].high;
    double mtf_low = bars[0].low;
    double mtf_close = bars[0].close;
    double mtf_volume = bars[0].volume;
    
    for (size_t i = 1; i < bars.size(); ++i) {
        long long bar_mtf_start = (bars[i].timestamp / mtf_duration_ms) * mtf_duration_ms;
        
        if (bar_mtf_start != current_mtf_start) {
            // New MTF period - save current MTF bar
            mtf_bars.emplace_back(mtf_open, mtf_high, mtf_low, mtf_close, mtf_volume, current_mtf_start);
            
            // Start new MTF bar
            current_mtf_start = bar_mtf_start;
            mtf_open = bars[i].open;
            mtf_high = bars[i].high;
            mtf_low = bars[i].low;
            mtf_close = bars[i].close;
            mtf_volume = bars[i].volume;
        } else {
            // Continue current MTF bar
            mtf_high = std::max(mtf_high, bars[i].high);
            mtf_low = std::min(mtf_low, bars[i].low);
            mtf_close = bars[i].close;
            mtf_volume += bars[i].volume;
        }
    }
    
    // Add the last MTF bar
    mtf_bars.emplace_back(mtf_open, mtf_high, mtf_low, mtf_close, mtf_volume, current_mtf_start);
    
    return mtf_bars;
}

bool FibonacciCalculator::isNewMTFPeriod(const OHLCBar& current_bar, const OHLCBar& previous_bar) {
    long long mtf_duration_ms = timeframeToMilliseconds(mtf_period_);
    long long current_mtf_start = (current_bar.timestamp / mtf_duration_ms) * mtf_duration_ms;
    long long previous_mtf_start = (previous_bar.timestamp / mtf_duration_ms) * mtf_duration_ms;
    
    return current_mtf_start != previous_mtf_start;
}

std::vector<TradingSignal> FibonacciCalculator::generateSignals() {
    signals_.clear();
    
    if (fibonacci_results_.empty() || data_.empty()) {
        return signals_;
    }
    
    // Generate signals based on price action hitting Fibonacci levels
    for (size_t i = 0; i < data_.size(); ++i) {
        const auto& bar = data_[i];
        
        // Find the most recent Fibonacci result for this bar
        FibonacciResult* current_fib = nullptr;
        for (auto& fib : fibonacci_results_) {
            if (bar.timestamp >= fib.start_time) {
                current_fib = &fib;
            }
        }
        
        if (!current_fib) continue;
        
        // Check for buy signal (price hits green level)
        if (!green_signal_triggered_ && 
            bar.high >= current_fib->green_level && 
            bar.low <= current_fib->green_level) {
            
            signals_.emplace_back(TradingSignal::BUY, current_fib->green_level, 
                                bar.timestamp, static_cast<int>(i));
            green_signal_triggered_ = true;
            active_buy_position_ = true;
            active_sell_position_ = false;
            
            // Calculate TP and SL levels
            auto [tp_buy, tp_sell] = calculateTPLevels(current_fib->range_low, current_fib->price_range);
            buy_tp_level_ = tp_buy;
            buy_sl_level_ = current_fib->level_0_9;  // Use 0.9 level for long stop loss
        }
        
        // Check for sell signal (price hits red level)
        if (!red_signal_triggered_ && 
            bar.low <= current_fib->red_level && 
            bar.high >= current_fib->red_level) {
            
            signals_.emplace_back(TradingSignal::SELL, current_fib->red_level, 
                                bar.timestamp, static_cast<int>(i));
            red_signal_triggered_ = true;
            active_sell_position_ = true;
            active_buy_position_ = false;
            
            // Calculate TP and SL levels
            auto [tp_buy, tp_sell] = calculateTPLevels(current_fib->range_low, current_fib->price_range);
            sell_tp_level_ = tp_sell;
            sell_sl_level_ = current_fib->level_0_1;  // Use 0.1 level for short stop loss
        }
        
        // Check for TP/SL conditions
        checkTPSLConditions(bar);
    }
    
    return signals_;
}

bool FibonacciCalculator::checkTPSLConditions(const OHLCBar& bar) {
    bool signal_generated = false;
    
    // Check buy TP
    if (active_buy_position_ && !std::isnan(buy_tp_level_) &&
        bar.high >= buy_tp_level_ && bar.low <= buy_tp_level_) {
        
        signals_.emplace_back(TradingSignal::TAKE_PROFIT, buy_tp_level_, 
                            bar.timestamp, static_cast<int>(signals_.size()));
        active_buy_position_ = false;
        buy_tp_level_ = std::numeric_limits<double>::quiet_NaN();
        buy_sl_level_ = std::numeric_limits<double>::quiet_NaN();
        signal_generated = true;
    }
    // Check buy SL
    else if (active_buy_position_ && !std::isnan(buy_sl_level_) &&
             bar.low <= buy_sl_level_ && bar.high >= buy_sl_level_) {
        
        signals_.emplace_back(TradingSignal::STOP_LOSS, buy_sl_level_, 
                            bar.timestamp, static_cast<int>(signals_.size()));
        active_buy_position_ = false;
        buy_tp_level_ = std::numeric_limits<double>::quiet_NaN();
        buy_sl_level_ = std::numeric_limits<double>::quiet_NaN();
        signal_generated = true;
    }
    
    // Check sell TP
    if (active_sell_position_ && !std::isnan(sell_tp_level_) &&
        bar.low <= sell_tp_level_ && bar.high >= sell_tp_level_) {
        
        signals_.emplace_back(TradingSignal::TAKE_PROFIT, sell_tp_level_, 
                            bar.timestamp, static_cast<int>(signals_.size()));
        active_sell_position_ = false;
        sell_tp_level_ = std::numeric_limits<double>::quiet_NaN();
        sell_sl_level_ = std::numeric_limits<double>::quiet_NaN();
        signal_generated = true;
    }
    // Check sell SL
    else if (active_sell_position_ && !std::isnan(sell_sl_level_) &&
             bar.high >= sell_sl_level_ && bar.low <= sell_sl_level_) {
        
        signals_.emplace_back(TradingSignal::STOP_LOSS, sell_sl_level_, 
                            bar.timestamp, static_cast<int>(signals_.size()));
        active_sell_position_ = false;
        sell_tp_level_ = std::numeric_limits<double>::quiet_NaN();
        sell_sl_level_ = std::numeric_limits<double>::quiet_NaN();
        signal_generated = true;
    }
    
    return signal_generated;
}

std::pair<double, double> FibonacciCalculator::calculateTPLevels(double range_low, double price_range) {
    double tp_buy = range_low + (price_range * 1.1);   // 1.1 level for buy TP
    double tp_sell = range_low + (price_range * -0.1); // -0.1 level for sell TP
    return {tp_buy, tp_sell};
}

double FibonacciCalculator::calculatePriceAtLevel(double range_low, double price_range, double fib_level) {
    return range_low + (price_range * fib_level);
}

std::vector<FibonacciLevel> FibonacciCalculator::getLevelsForRange(double range_low, double price_range) {
    std::vector<FibonacciLevel> levels;
    levels.reserve(DEFAULT_FIB_LEVELS.size());
    
    for (double level : DEFAULT_FIB_LEVELS) {
        double price = calculatePriceAtLevel(range_low, price_range, level);
        levels.emplace_back(level, price);
    }
    
    return levels;
}

void FibonacciCalculator::cleanup() {
    // Keep only recent results to maintain performance
    if (fibonacci_results_.size() > static_cast<size_t>(max_bars_back_)) {
        fibonacci_results_.erase(fibonacci_results_.begin(), 
                               fibonacci_results_.end() - max_bars_back_);
    }
    
    if (signals_.size() > static_cast<size_t>(max_bars_back_ * 2)) {
        signals_.erase(signals_.begin(), 
                      signals_.end() - max_bars_back_ * 2);
    }
}

void FibonacciCalculator::reserveCapacity(size_t capacity) {
    data_.reserve(capacity);
    fibonacci_results_.reserve(capacity);
    signals_.reserve(capacity * 2);
}

// Helper functions
long long timeframeToMilliseconds(int timeframe_minutes) {
    return static_cast<long long>(timeframe_minutes) * 60 * 1000;
}

int calculateBarsInTimeframe(int higher_tf_minutes, int current_tf_minutes) {
    return higher_tf_minutes / current_tf_minutes;
}

bool isNewTimeframePeriod(long long current_time, long long previous_time, int timeframe_minutes) {
    long long tf_duration_ms = timeframeToMilliseconds(timeframe_minutes);
    long long current_tf_start = (current_time / tf_duration_ms) * tf_duration_ms;
    long long previous_tf_start = (previous_time / tf_duration_ms) * tf_duration_ms;
    return current_tf_start != previous_tf_start;
}

} // namespace trading

// Python bindings
namespace py = pybind11;

PYBIND11_MODULE(fibonacci_calc, m) {
    m.doc() = "High-performance Fibonacci calculator for trading applications";

    // OHLCBar struct
    py::class_<trading::OHLCBar>(m, "OHLCBar")
        .def(py::init<double, double, double, double, double, long long>(),
             "open"_a, "high"_a, "low"_a, "close"_a, "volume"_a, "timestamp"_a)
        .def_readwrite("open", &trading::OHLCBar::open)
        .def_readwrite("high", &trading::OHLCBar::high)
        .def_readwrite("low", &trading::OHLCBar::low)
        .def_readwrite("close", &trading::OHLCBar::close)
        .def_readwrite("volume", &trading::OHLCBar::volume)
        .def_readwrite("timestamp", &trading::OHLCBar::timestamp);

    // FibonacciLevel struct
    py::class_<trading::FibonacciLevel>(m, "FibonacciLevel")
        .def(py::init<double, double>(), "level"_a, "price"_a)
        .def_readwrite("level", &trading::FibonacciLevel::level)
        .def_readwrite("price", &trading::FibonacciLevel::price);

    // FibonacciResult struct
    py::class_<trading::FibonacciResult>(m, "FibonacciResult")
        .def(py::init<>())
        .def_readwrite("levels", &trading::FibonacciResult::levels)
        .def_readwrite("range_high", &trading::FibonacciResult::range_high)
        .def_readwrite("range_low", &trading::FibonacciResult::range_low)
        .def_readwrite("price_range", &trading::FibonacciResult::price_range)
        .def_readwrite("start_time", &trading::FibonacciResult::start_time)
        .def_readwrite("end_time", &trading::FibonacciResult::end_time)
        .def_readwrite("is_bullish", &trading::FibonacciResult::is_bullish)
        .def_readwrite("green_level", &trading::FibonacciResult::green_level)
        .def_readwrite("red_level", &trading::FibonacciResult::red_level);

    // TradingSignal struct
    py::enum_<trading::TradingSignal::Type>(m, "SignalType")
        .value("BUY", trading::TradingSignal::Type::BUY)
        .value("SELL", trading::TradingSignal::Type::SELL)
        .value("TAKE_PROFIT", trading::TradingSignal::Type::TAKE_PROFIT)
        .value("STOP_LOSS", trading::TradingSignal::Type::STOP_LOSS);

    py::class_<trading::TradingSignal>(m, "TradingSignal")
        .def(py::init<trading::TradingSignal::Type, double, long long, int, bool>(),
             "type"_a, "price"_a, "timestamp"_a, "bar_index"_a, "is_delayed"_a = false)
        .def_readwrite("type", &trading::TradingSignal::type)
        .def_readwrite("price", &trading::TradingSignal::price)
        .def_readwrite("timestamp", &trading::TradingSignal::timestamp)
        .def_readwrite("bar_index", &trading::TradingSignal::bar_index)
        .def_readwrite("is_delayed", &trading::TradingSignal::is_delayed);

    // FibonacciCalculator class
    py::class_<trading::FibonacciCalculator>(m, "FibonacciCalculator")
        .def(py::init<>())
        .def("set_mtf_mode", &trading::FibonacciCalculator::setMTFMode,
             "enabled"_a, "mtf_period"_a = 5, "current_tf_period"_a = 1)
        .def("set_confirmed_data", &trading::FibonacciCalculator::setConfirmedData)
        .def("set_max_bars_back", &trading::FibonacciCalculator::setMaxBarsBack)
        .def("set_data", &trading::FibonacciCalculator::setData)
        .def("add_bar", &trading::FibonacciCalculator::addBar)
        .def("calculate_fibonacci", &trading::FibonacciCalculator::calculateFibonacci)
        .def("generate_signals", &trading::FibonacciCalculator::generateSignals)
        .def("get_fibonacci_results", &trading::FibonacciCalculator::getFibonacciResults,
             py::return_value_policy::reference_internal)
        .def("get_signals", &trading::FibonacciCalculator::getSignals,
             py::return_value_policy::reference_internal)
        .def("cleanup", &trading::FibonacciCalculator::cleanup)
        .def("reserve_capacity", &trading::FibonacciCalculator::reserveCapacity);

    // Helper functions
    m.def("timeframe_to_milliseconds", &trading::timeframeToMilliseconds);
    m.def("calculate_bars_in_timeframe", &trading::calculateBarsInTimeframe);
    m.def("is_new_timeframe_period", &trading::isNewTimeframePeriod);
}
